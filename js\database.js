// Database Simulation using Local Storage
class DatabaseManager {
    constructor() {
        this.tables = {
            incoming: 'incoming_correspondence',
            outgoing: 'outgoing_correspondence',
            entities: 'external_entities',
            users: 'system_users'
        };
        this.initializeDatabase();
    }

    // Initialize database with default data
    initializeDatabase() {
        // Initialize users table with default admin user
        if (!this.getTable('users').length) {
            this.insertRecord('users', {
                id: 1,
                username: 'admin',
                password: 'admin123', // In real app, this should be hashed
                fullName: 'مدير النظام',
                role: 'administrator',
                email: '<EMAIL>',
                phone: '',
                createdDate: new Date().toISOString(),
                isActive: true
            });

            // Add sample employee user
            this.insertRecord('users', {
                id: 2,
                username: 'employee1',
                password: 'emp123',
                fullName: 'موظف النظام',
                role: 'employee',
                email: '<EMAIL>',
                phone: '',
                createdDate: new Date().toISOString(),
                isActive: true
            });
        }

        // Initialize entities table with sample data
        if (!this.getTable('entities').length) {
            const sampleEntities = [
                {
                    id: 1,
                    name: 'وزارة التربية والتعليم',
                    type: 'وزارة',
                    address: 'الرياض، المملكة العربية السعودية',
                    phone: '+966-11-1234567',
                    fax: '+966-11-1234568',
                    email: '<EMAIL>',
                    createdDate: new Date().toISOString()
                },
                {
                    id: 2,
                    name: 'شركة الاتصالات السعودية',
                    type: 'شركة',
                    address: 'جدة، المملكة العربية السعودية',
                    phone: '+966-12-9876543',
                    fax: '+966-12-9876544',
                    email: '<EMAIL>',
                    createdDate: new Date().toISOString()
                }
            ];

            sampleEntities.forEach(entity => {
                this.insertRecord('entities', entity);
            });
        }
    }

    // Get all records from a table
    getTable(tableName) {
        const key = this.tables[tableName];
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : [];
    }

    // Save table data
    saveTable(tableName, data) {
        const key = this.tables[tableName];
        localStorage.setItem(key, JSON.stringify(data));
    }

    // Insert new record
    insertRecord(tableName, record) {
        const table = this.getTable(tableName);
        
        // Auto-generate ID if not provided
        if (!record.id) {
            record.id = this.getNextId(tableName);
        }
        
        // Add timestamps
        record.createdDate = record.createdDate || new Date().toISOString();
        record.updatedDate = new Date().toISOString();
        
        table.push(record);
        this.saveTable(tableName, table);
        return record;
    }

    // Update existing record
    updateRecord(tableName, id, updates) {
        const table = this.getTable(tableName);
        const index = table.findIndex(record => record.id == id);
        
        if (index !== -1) {
            table[index] = { ...table[index], ...updates, updatedDate: new Date().toISOString() };
            this.saveTable(tableName, table);
            return table[index];
        }
        
        return null;
    }

    // Delete record
    deleteRecord(tableName, id) {
        const table = this.getTable(tableName);
        const filteredTable = table.filter(record => record.id != id);
        
        if (filteredTable.length !== table.length) {
            this.saveTable(tableName, filteredTable);
            return true;
        }
        
        return false;
    }

    // Get record by ID
    getRecord(tableName, id) {
        const table = this.getTable(tableName);
        return table.find(record => record.id == id) || null;
    }

    // Search records
    searchRecords(tableName, searchTerm, fields = []) {
        const table = this.getTable(tableName);
        
        if (!searchTerm) return table;
        
        const term = searchTerm.toLowerCase();
        
        return table.filter(record => {
            if (fields.length === 0) {
                // Search in all string fields
                return Object.values(record).some(value => 
                    typeof value === 'string' && value.toLowerCase().includes(term)
                );
            } else {
                // Search in specific fields
                return fields.some(field => 
                    record[field] && 
                    typeof record[field] === 'string' && 
                    record[field].toLowerCase().includes(term)
                );
            }
        });
    }

    // Filter records by criteria
    filterRecords(tableName, criteria) {
        const table = this.getTable(tableName);
        
        return table.filter(record => {
            return Object.keys(criteria).every(key => {
                if (criteria[key] === null || criteria[key] === undefined || criteria[key] === '') {
                    return true; // Skip empty criteria
                }
                return record[key] === criteria[key];
            });
        });
    }

    // Get next available ID
    getNextId(tableName) {
        const table = this.getTable(tableName);
        if (table.length === 0) return 1;
        
        const maxId = Math.max(...table.map(record => record.id || 0));
        return maxId + 1;
    }

    // Get records with pagination
    getPaginatedRecords(tableName, page = 1, pageSize = 10, searchTerm = '', filters = {}) {
        let records = this.getTable(tableName);
        
        // Apply search
        if (searchTerm) {
            records = this.searchRecords(tableName, searchTerm);
        }
        
        // Apply filters
        if (Object.keys(filters).length > 0) {
            records = records.filter(record => {
                return Object.keys(filters).every(key => {
                    if (filters[key] === null || filters[key] === undefined || filters[key] === '') {
                        return true;
                    }
                    return record[key] === filters[key];
                });
            });
        }
        
        // Calculate pagination
        const totalRecords = records.length;
        const totalPages = Math.ceil(totalRecords / pageSize);
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedRecords = records.slice(startIndex, endIndex);
        
        return {
            records: paginatedRecords,
            pagination: {
                currentPage: page,
                pageSize: pageSize,
                totalRecords: totalRecords,
                totalPages: totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1
            }
        };
    }

    // User authentication
    authenticateUser(username, password) {
        const users = this.getTable('users');
        const user = users.find(u => 
            u.username === username && 
            u.password === password && 
            u.isActive
        );
        
        if (user) {
            // Store current user session
            const userSession = {
                id: user.id,
                username: user.username,
                fullName: user.fullName,
                role: user.role,
                email: user.email,
                loginTime: new Date().toISOString()
            };
            
            localStorage.setItem('currentUser', JSON.stringify(userSession));
            return userSession;
        }
        
        return null;
    }

    // Get current user
    getCurrentUser() {
        const userData = localStorage.getItem('currentUser');
        return userData ? JSON.parse(userData) : null;
    }

    // Logout user
    logoutUser() {
        localStorage.removeItem('currentUser');
    }

    // Check if user has permission
    hasPermission(action, resource) {
        const user = this.getCurrentUser();
        if (!user) return false;
        
        const permissions = {
            administrator: ['create', 'read', 'update', 'delete', 'manage_users'],
            employee: ['create', 'read', 'update'],
            readonly: ['read']
        };
        
        const userPermissions = permissions[user.role] || [];
        return userPermissions.includes(action);
    }

    // Export data
    exportTable(tableName) {
        const table = this.getTable(tableName);
        return JSON.stringify(table, null, 2);
    }

    // Import data
    importTable(tableName, jsonData) {
        try {
            const data = JSON.parse(jsonData);
            if (Array.isArray(data)) {
                this.saveTable(tableName, data);
                return true;
            }
        } catch (error) {
            console.error('Import error:', error);
        }
        return false;
    }

    // Clear all data (for testing)
    clearAllData() {
        Object.values(this.tables).forEach(key => {
            localStorage.removeItem(key);
        });
        this.initializeDatabase();
    }

    // Get statistics
    getStatistics() {
        const incoming = this.getTable('incoming');
        const outgoing = this.getTable('outgoing');
        const entities = this.getTable('entities');
        const users = this.getTable('users');
        
        return {
            totalIncoming: incoming.length,
            totalOutgoing: outgoing.length,
            totalEntities: entities.length,
            totalUsers: users.length,
            pendingIncoming: incoming.filter(r => !r.responseDate).length,
            thisMonthIncoming: incoming.filter(r => {
                const date = new Date(r.receptionDate);
                const now = new Date();
                return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
            }).length,
            thisMonthOutgoing: outgoing.filter(r => {
                const date = new Date(r.sendingDate);
                const now = new Date();
                return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
            }).length
        };
    }
}

// Global database instance
const db = new DatabaseManager();
