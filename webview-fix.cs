// حل مشكلة الارتجاج في WebView2 (C#)
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;

public partial class MainForm : Form
{
    private WebView2 webView;
    
    public MainForm()
    {
        InitializeComponent();
        InitializeWebView();
    }
    
    private async void InitializeWebView()
    {
        // إنشاء WebView2
        webView = new WebView2()
        {
            Dock = DockStyle.Fill
        };
        
        // إعدادات منع الارتجاج
        var options = CoreWebView2Environment.CreateAsync(
            browserExecutableFolder: null,
            userDataFolder: null,
            options: new CoreWebView2EnvironmentOptions()
            {
                // إيقاف تسريع الأجهزة
                AdditionalBrowserArguments = 
                    "--disable-gpu " +
                    "--disable-gpu-compositing " +
                    "--disable-gpu-vsync " +
                    "--disable-features=VizDisplayCompositor " +
                    "--disable-software-rasterizer " +
                    "--disable-background-timer-throttling " +
                    "--disable-renderer-backgrounding " +
                    "--disable-backgrounding-occluded-windows"
            });
        
        this.Controls.Add(webView);
        
        // انتظار التهيئة
        await webView.EnsureCoreWebView2Async();
        
        // إعدادات إضافية
        webView.CoreWebView2.Settings.IsSwipeNavigationEnabled = false;
        webView.CoreWebView2.Settings.AreBrowserAcceleratorKeysEnabled = false;
        webView.CoreWebView2.Settings.AreDevToolsEnabled = false;
        webView.CoreWebView2.Settings.AreHostObjectsAllowed = false;
        webView.CoreWebView2.Settings.IsGeneralAutofillEnabled = false;
        
        // منع الوميض عند التحميل
        webView.CoreWebView2.DOMContentLoaded += async (sender, args) =>
        {
            // إضافة CSS لمنع الوميض
            await webView.CoreWebView2.AddWebResourceRequestedFilterAsync("*", CoreWebView2WebResourceContext.Document);
            
            await webView.CoreWebView2.ExecuteScriptAsync(@"
                // منع الوميض
                document.documentElement.style.opacity = '1';
                document.body.style.opacity = '1';
                
                // إيقاف جميع الحركات
                const style = document.createElement('style');
                style.textContent = `
                    *, *::before, *::after {
                        transition: none !important;
                        animation: none !important;
                        transform: none !important;
                    }
                `;
                document.head.appendChild(style);
            ");
        };
        
        // تحميل الصفحة
        string htmlPath = Path.Combine(Application.StartupPath, "index.html");
        webView.CoreWebView2.Navigate($"file:///{htmlPath}");
    }
    
    // منع تغيير حجم النافذة لتجنب الوميض
    protected override void SetBoundsCore(int x, int y, int width, int height, BoundsSpecified specified)
    {
        base.SetBoundsCore(x, y, this.Width, this.Height, specified);
    }
}
