<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المراسلات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div class="row w-100">
            <div class="col-md-6 col-lg-4 mx-auto">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </h3>
                    </div>
                    <div class="card-body p-5">
                        <!-- System Status -->
                        <div id="systemStatus" class="alert alert-info text-center mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <span id="statusMessage">النظام جاهز للاستخدام</span>
                        </div>

                        <!-- Login Form -->
                        <form id="loginForm">
                            <div class="mb-4">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-2"></i>
                                    اسم المستخدم
                                </label>
                                <input type="text" class="form-control form-control-lg" 
                                       id="username" name="username" required 
                                       placeholder="أدخل اسم المستخدم">
                            </div>

                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>
                                    كلمة المرور
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control form-control-lg" 
                                           id="password" name="password" required 
                                           placeholder="أدخل كلمة المرور">
                                    <button class="btn btn-outline-secondary" type="button" 
                                            onclick="togglePasswordVisibility()">
                                        <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-4 form-check">
                                <input type="checkbox" class="form-check-input" id="rememberMe">
                                <label class="form-check-label" for="rememberMe">
                                    تذكرني
                                </label>
                            </div>

                            <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                دخول
                            </button>
                        </form>

                        <hr class="my-4">

                        <!-- Default Users Info -->
                        <div class="text-center">
                            <h6 class="text-muted mb-3">المستخدمين الافتراضيين:</h6>
                            <div class="row">
                                <div class="col-6">
                                    <div class="card bg-light">
                                        <div class="card-body p-3">
                                            <h6 class="card-title mb-1">مدير النظام</h6>
                                            <p class="card-text small mb-1">
                                                <strong>المستخدم:</strong> admin<br>
                                                <strong>كلمة المرور:</strong> admin123
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card bg-light">
                                        <div class="card-body p-3">
                                            <h6 class="card-title mb-1">موظف</h6>
                                            <p class="card-text small mb-1">
                                                <strong>المستخدم:</strong> employee1<br>
                                                <strong>كلمة المرور:</strong> emp123
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- Back to Activation -->
                        <div class="text-center">
                            <a href="index.html" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة لصفحة التفعيل
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/database.js"></script>
    <script src="js/activation.js"></script>
    <script src="js/app.js"></script>
    
    <script>
        // Check system access and update status
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize activation system
            if (!activationSystem) {
                activationSystem = new ActivationSystem();
            }

            // Check if user can access the system
            if (!activationSystem.canAccessSystem()) {
                window.location.href = 'index.html';
                return;
            }

            // Update system status display
            updateSystemStatus();

            // Auto-fill demo credentials on demo button click
            setupDemoLogin();
        });

        function updateSystemStatus() {
            const statusElement = document.getElementById('systemStatus');
            const messageElement = document.getElementById('statusMessage');
            
            if (activationSystem && statusElement && messageElement) {
                const status = activationSystem.getSystemStatus();
                
                statusElement.className = `alert alert-${status.class} text-center mb-4`;
                messageElement.textContent = status.message;
            }
        }

        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        function setupDemoLogin() {
            // Add click handlers for demo login cards
            const adminCard = document.querySelector('.col-6:first-child .card');
            const employeeCard = document.querySelector('.col-6:last-child .card');
            
            if (adminCard) {
                adminCard.style.cursor = 'pointer';
                adminCard.addEventListener('click', function() {
                    document.getElementById('username').value = 'admin';
                    document.getElementById('password').value = 'admin123';
                    this.classList.add('border-primary');
                    setTimeout(() => this.classList.remove('border-primary'), 1000);
                });
            }
            
            if (employeeCard) {
                employeeCard.style.cursor = 'pointer';
                employeeCard.addEventListener('click', function() {
                    document.getElementById('username').value = 'employee1';
                    document.getElementById('password').value = 'emp123';
                    this.classList.add('border-success');
                    setTimeout(() => this.classList.remove('border-success'), 1000);
                });
            }
        }

        // Handle Enter key in form fields
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && (e.target.id === 'username' || e.target.id === 'password')) {
                e.preventDefault();
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
