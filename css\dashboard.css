/* Dashboard Specific Styles */

/* Top Navigation */
.top-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: var(--white);
    z-index: 1000;
    box-shadow: var(--shadow);
}

.nav-container {
    height: 100%;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 16px;
}

.brand-icon {
    font-size: 32px;
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-title {
    font-size: 18px;
    font-weight: 700;
    line-height: 1.2;
}

.brand-subtitle {
    font-size: 14px;
    opacity: 0.8;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-info {
    text-align: left;
}

.user-name {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.2;
}

.user-role {
    font-size: 13px;
    opacity: 0.8;
}

.user-menu {
    position: relative;
}

.user-menu-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 18px;
    cursor: pointer;
    color: var(--white);
}

.user-menu-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    min-width: 150px;
    padding: 8px 0;
    display: none;
    z-index: 1001;
}

.user-dropdown a {
    display: block;
    padding: 12px 16px;
    color: var(--gray-700);
    text-decoration: none;
    font-size: 14px;
}

.user-dropdown a:hover {
    background: var(--gray-100);
}

/* Dashboard Container */
.dashboard-container {
    display: flex;
    min-height: 100vh;
    padding-top: 70px;
}

/* Side Navigation */
.side-nav {
    width: 250px;
    background: var(--white);
    border-left: 1px solid var(--gray-200);
    padding: 24px 0;
    position: fixed;
    top: 70px;
    bottom: 0;
    overflow-y: auto;
}

.nav-menu {
    padding: 0 16px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    margin-bottom: 8px;
    border-radius: var(--radius);
    color: var(--gray-600);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.nav-item:hover {
    background: var(--gray-100);
    color: var(--primary);
}

.nav-item.active {
    background: var(--primary);
    color: var(--white);
}

.nav-icon {
    font-size: 20px;
    width: 24px;
    text-align: center;
}

.nav-text {
    font-size: 15px;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-right: 250px;
    padding: 32px;
    background: var(--gray-50);
    min-height: calc(100vh - 70px);
}

/* Welcome Section */
.welcome-section {
    margin-bottom: 32px;
}

.welcome-card {
    background: var(--white);
    border-radius: var(--radius);
    padding: 32px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 24px;
}

.welcome-icon {
    font-size: 48px;
    flex-shrink: 0;
}

.welcome-text h1 {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 8px;
    line-height: 1.3;
}

.welcome-text p {
    font-size: 16px;
    color: var(--gray-600);
    margin: 0;
    line-height: 1.5;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius);
    padding: 24px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
}

.stat-card.primary::before { background: var(--primary); }
.stat-card.success::before { background: var(--success); }
.stat-card.warning::before { background: var(--warning); }
.stat-card.info::before { background: var(--info); }

.stat-icon {
    font-size: 36px;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--gray-800);
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: var(--gray-600);
    line-height: 1.3;
}

/* Actions Section */
.actions-section {
    margin-bottom: 32px;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.section-icon {
    font-size: 24px;
}

.section-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--gray-800);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.action-card {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius);
    padding: 24px;
    text-align: center;
    text-decoration: none;
    color: var(--gray-700);
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.action-card.primary:hover { border-color: var(--primary); color: var(--primary); }
.action-card.success:hover { border-color: var(--success); color: var(--success); }
.action-card.warning:hover { border-color: var(--warning); color: var(--warning); }
.action-card.info:hover { border-color: var(--info); color: var(--info); }

.action-icon {
    font-size: 32px;
}

.action-text {
    font-size: 15px;
    font-weight: 600;
}

/* Tables Section */
.tables-section {
    margin-bottom: 32px;
}

.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
}

.table-card {
    background: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.table-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.table-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-800);
}

.table-icon {
    font-size: 20px;
}

.view-all-btn {
    color: var(--primary);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.view-all-btn:hover {
    text-decoration: underline;
}

.table-content {
    padding: 0;
}

.simple-table {
    width: 100%;
}

.table-row {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 2fr;
    gap: 16px;
    padding: 16px 24px;
    border-bottom: 1px solid var(--gray-100);
}

.table-row.header {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 14px;
}

.table-row:not(.header):hover {
    background: var(--gray-50);
}

.table-cell {
    font-size: 14px;
    color: var(--gray-600);
    line-height: 1.4;
}

.table-cell.loading {
    grid-column: 1 / -1;
    text-align: center;
    color: var(--gray-500);
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .side-nav {
        width: 200px;
    }
    
    .main-content {
        margin-right: 200px;
        padding: 24px;
    }
    
    .nav-text {
        font-size: 14px;
    }
}

@media (max-width: 768px) {
    .side-nav {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .main-content {
        margin-right: 0;
        padding: 20px;
    }
    
    .welcome-card {
        flex-direction: column;
        text-align: center;
        padding: 24px;
    }
    
    .welcome-text h1 {
        font-size: 24px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    .tables-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .table-row {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .table-cell {
        padding: 4px 0;
    }
    
    .brand-text {
        display: none;
    }
    
    .user-info {
        display: none;
    }
}
