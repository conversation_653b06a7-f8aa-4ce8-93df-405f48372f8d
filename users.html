<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - نظام إدارة المراسلات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-envelope-open-text me-2"></i>
                نظام إدارة المراسلات
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="incoming.html">
                            <i class="fas fa-inbox me-1"></i>
                            المراسلات الواردة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="outgoing.html">
                            <i class="fas fa-paper-plane me-1"></i>
                            المراسلات الصادرة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="entities.html">
                            <i class="fas fa-building me-1"></i>
                            الجهات الخارجية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="users.html">
                            <i class="fas fa-users me-1"></i>
                            إدارة المستخدمين
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <span id="currentUserName">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><span class="dropdown-item-text">الدور: <span id="currentUserRole">موظف</span></span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="app.logout()">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2 class="card-title text-gradient mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    إدارة المستخدمين
                                </h2>
                                <p class="card-text text-muted">
                                    إضافة وتعديل وإدارة مستخدمي النظام وصلاحياتهم
                                </p>
                            </div>
                            <button class="btn btn-warning btn-lg" data-bs-toggle="modal" data-bs-target="#userModal" id="addUserBtn">
                                <i class="fas fa-user-plus me-2"></i>
                                إضافة مستخدم جديد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Access Control Alert -->
        <div class="row mb-4" id="accessAlert" style="display: none;">
            <div class="col-12">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ليس لديك صلاحية لإدارة المستخدمين. هذه الصفحة متاحة للمديرين فقط.
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="row" id="usersContent">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة المستخدمين
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="usersTable">
                                <thead>
                                    <tr>
                                        <th>الاسم الكامل</th>
                                        <th>اسم المستخدم</th>
                                        <th>الدور</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="7" class="text-center">جاري التحميل...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit User Modal -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة مستخدم جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="fullName" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="fullName" name="fullName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">اسم المستخدم *</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">كلمة المرور *</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">الدور *</label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">اختر الدور</option>
                                    <option value="administrator">مدير</option>
                                    <option value="employee">موظف</option>
                                    <option value="readonly">قراءة فقط</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isActive" name="isActive" checked>
                                <label class="form-check-label" for="isActive">
                                    المستخدم نشط
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning" onclick="saveUser()">
                        <i class="fas fa-save me-2"></i>
                        حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/database.js"></script>
    <script src="js/activation.js"></script>
    <script src="js/app.js"></script>
    
    <script>
        // Page specific functions
        function saveUser() {
            const form = document.getElementById('userForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            // Convert checkbox value
            data.isActive = document.getElementById('isActive').checked;
            
            // Validate required fields
            if (!data.fullName || !data.username || !data.password || !data.role) {
                app.showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }
            
            // Check if username already exists (for new users)
            const recordId = form.dataset.recordId;
            if (!recordId) {
                const existingUsers = db.getTable('users');
                if (existingUsers.some(user => user.username === data.username)) {
                    app.showMessage('اسم المستخدم موجود بالفعل', 'danger');
                    return;
                }
            }
            
            if (recordId) {
                // Update existing record
                db.updateRecord('users', recordId, data);
                app.showMessage('تم تحديث المستخدم بنجاح', 'success');
            } else {
                // Add new record
                db.insertRecord('users', data);
                app.showMessage('تم إضافة المستخدم بنجاح', 'success');
            }
            
            // Close modal and refresh table
            const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
            modal.hide();
            form.reset();
            delete form.dataset.recordId;
            loadUsersTable();
        }
        
        function editUser(id) {
            const user = db.getRecord('users', id);
            if (user) {
                // Populate form
                Object.keys(user).forEach(key => {
                    const field = document.getElementById(key);
                    if (field) {
                        if (field.type === 'checkbox') {
                            field.checked = user[key];
                        } else {
                            field.value = user[key] || '';
                        }
                    }
                });
                
                // Store record ID
                const form = document.getElementById('userForm');
                form.dataset.recordId = user.id;
                
                // Update modal title
                document.querySelector('#userModal .modal-title').innerHTML = 
                    '<i class="fas fa-user-edit me-2"></i>تعديل مستخدم';
                
                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('userModal'));
                modal.show();
            }
        }
        
        function deleteUser(id) {
            // Prevent deleting admin user
            if (id == 1) {
                app.showMessage('لا يمكن حذف المستخدم الرئيسي', 'danger');
                return;
            }
            
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                if (db.deleteRecord('users', id)) {
                    app.showMessage('تم حذف المستخدم بنجاح', 'success');
                    loadUsersTable();
                } else {
                    app.showMessage('حدث خطأ أثناء الحذف', 'danger');
                }
            }
        }
        
        function toggleUserStatus(id) {
            const user = db.getRecord('users', id);
            if (user) {
                user.isActive = !user.isActive;
                db.updateRecord('users', id, { isActive: user.isActive });
                app.showMessage(`تم ${user.isActive ? 'تفعيل' : 'إلغاء تفعيل'} المستخدم`, 'success');
                loadUsersTable();
            }
        }
        
        function loadUsersTable() {
            const users = db.getTable('users');
            renderUsersTable(users);
        }
        
        function renderUsersTable(users) {
            const tableBody = document.querySelector('#usersTable tbody');
            tableBody.innerHTML = '';
            
            if (users.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center">لا يوجد مستخدمين</td></tr>';
                return;
            }
            
            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.fullName}</td>
                    <td>${user.username}</td>
                    <td><span class="badge bg-${getRoleBadgeColor(user.role)}">${app.getRoleDisplayName(user.role)}</span></td>
                    <td>${user.email || ''}</td>
                    <td>
                        <span class="badge bg-${user.isActive ? 'success' : 'danger'}">
                            ${user.isActive ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td>${app.formatDate(user.createdDate)}</td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editUser(${user.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-${user.isActive ? 'warning' : 'success'} me-1" 
                                onclick="toggleUserStatus(${user.id})" 
                                title="${user.isActive ? 'إلغاء التفعيل' : 'تفعيل'}">
                            <i class="fas fa-${user.isActive ? 'user-slash' : 'user-check'}"></i>
                        </button>
                        ${user.id !== 1 ? `<button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})" title="حذف"><i class="fas fa-trash"></i></button>` : ''}
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }
        
        function getRoleBadgeColor(role) {
            const colors = {
                'administrator': 'danger',
                'employee': 'primary',
                'readonly': 'secondary'
            };
            return colors[role] || 'secondary';
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            if (!checkSystemAccess()) {
                return;
            }
            
            // Check if user has permission to manage users
            if (!db.hasPermission('manage_users')) {
                document.getElementById('accessAlert').style.display = 'block';
                document.getElementById('usersContent').style.display = 'none';
                document.getElementById('addUserBtn').style.display = 'none';
                return;
            }
            
            loadUsersTable();
            
            // Reset modal on close
            document.getElementById('userModal').addEventListener('hidden.bs.modal', function() {
                document.getElementById('userForm').reset();
                delete document.getElementById('userForm').dataset.recordId;
                document.querySelector('#userModal .modal-title').innerHTML = 
                    '<i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد';
                document.getElementById('isActive').checked = true;
            });
        });
    </script>
</body>
</html>
