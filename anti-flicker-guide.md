# دليل حل مشكلة الوميض والارتجاج في تطبيقات HTML

## 🔍 **تشخيص المشكلة:**

### **الأسباب الشائعة:**
1. **تسريع الأجهزة (Hardware Acceleration)**
2. **إعدادات WebView/Browser Engine**
3. **CSS Transitions والAnimations**
4. **تحديثات DOM المتكررة**
5. **إعدادات النافذة**

## 🔧 **الحلول حسب نوع التطبيق:**

### **1. Electron Apps:**
```javascript
// في main.js
const mainWindow = new BrowserWindow({
    show: false, // لا تظهر حتى التحميل
    backgroundColor: '#1a365d',
    webPreferences: {
        hardwareAcceleration: false, // إيقاف تسريع الأجهزة
        offscreen: false,
        paintWhenInitiallyHidden: false
    }
});

// إظهار بعد التحميل
mainWindow.once('ready-to-show', () => {
    mainWindow.show();
});

// إعدادات سطر الأوامر
app.commandLine.appendSwitch('disable-gpu-vsync');
app.commandLine.appendSwitch('disable-gpu-compositing');
```

### **2. WebView2 (C#):**
```csharp
// إعدادات البيئة
var options = new CoreWebView2EnvironmentOptions()
{
    AdditionalBrowserArguments = 
        "--disable-gpu " +
        "--disable-gpu-compositing " +
        "--disable-gpu-vsync " +
        "--disable-features=VizDisplayCompositor"
};

// إعدادات WebView
webView.CoreWebView2.Settings.IsSwipeNavigationEnabled = false;
webView.CoreWebView2.Settings.AreBrowserAcceleratorKeysEnabled = false;
```

### **3. CEF (Chromium Embedded Framework):**
```cpp
// إعدادات CEF
CefSettings settings;
settings.multi_threaded_message_loop = false;
settings.no_sandbox = true;

// إعدادات المتصفح
CefBrowserSettings browser_settings;
browser_settings.webgl = STATE_DISABLED;
browser_settings.accelerated_compositing = STATE_DISABLED;
```

### **4. JavaFX WebView:**
```java
// إعدادات النظام
System.setProperty("prism.vsync", "false");
System.setProperty("javafx.animation.pulse", "60");
System.setProperty("prism.lcdtext", "false");

// إعدادات WebView
WebView webView = new WebView();
webView.getEngine().setUserStyleSheetLocation(
    getClass().getResource("anti-flicker.css").toString()
);
```

## 🎨 **حلول CSS:**

### **منع الوميض عند التحميل:**
```css
/* إخفاء الصفحة أثناء التحميل */
html {
    opacity: 0;
    transition: opacity 0.1s ease-in;
}

html.loaded {
    opacity: 1;
}

/* منع جميع الحركات */
*, *::before, *::after {
    transition: none !important;
    animation: none !important;
    transform: none !important;
    -webkit-backface-visibility: hidden !important;
}
```

### **تثبيت العناصر:**
```css
/* تثبيت الصفحة */
html, body {
    position: fixed !important;
    overflow: hidden !important;
    width: 100vw !important;
    height: 100vh !important;
}

/* إيقاف تسريع الأجهزة */
* {
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    -webkit-transform-style: flat !important;
    transform-style: flat !important;
}
```

## 📱 **حلول JavaScript:**

### **منع الوميض:**
```javascript
// إظهار الصفحة بعد التحميل
window.addEventListener('load', function() {
    document.documentElement.classList.add('loaded');
    document.documentElement.style.opacity = '1';
});

// منع التحديثات المتكررة
let isUpdating = false;
function updateUI() {
    if (isUpdating) return;
    isUpdating = true;
    
    // تحديث العناصر
    requestAnimationFrame(() => {
        isUpdating = false;
    });
}
```

### **منع الأحداث المسببة للوميض:**
```javascript
// منع التمرير
document.addEventListener('wheel', e => e.preventDefault(), {passive: false});
document.addEventListener('scroll', e => e.preventDefault(), {passive: false});

// منع اللمس
document.addEventListener('touchmove', e => e.preventDefault(), {passive: false});

// منع تغيير الحجم
window.addEventListener('resize', e => e.preventDefault(), {passive: false});
```

## ⚙️ **إعدادات النظام:**

### **Windows:**
1. **إعدادات العرض:**
   - تعطيل "Enhanced pointer precision"
   - تعطيل "Smooth edges of screen fonts"

2. **إعدادات الرسومات:**
   - تعطيل Hardware Acceleration في المتصفح
   - تحديث تعريفات كارت الرسومات

### **إعدادات المتصفح:**
```
chrome://flags/
- Hardware-accelerated video decode: Disabled
- GPU rasterization: Disabled
- Smooth Scrolling: Disabled
```

## 🔧 **أدوات التشخيص:**

### **فحص الأداء:**
```javascript
// مراقبة FPS
let fps = 0;
let lastTime = performance.now();

function checkFPS() {
    const now = performance.now();
    fps = 1000 / (now - lastTime);
    lastTime = now;
    
    console.log('FPS:', Math.round(fps));
    requestAnimationFrame(checkFPS);
}
checkFPS();
```

### **فحص الوميض:**
```javascript
// مراقبة تغييرات DOM
const observer = new MutationObserver(mutations => {
    console.log('DOM changes:', mutations.length);
});

observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true
});
```

## ✅ **قائمة التحقق:**

- [ ] إيقاف Hardware Acceleration
- [ ] تعطيل CSS Transitions/Animations
- [ ] تثبيت موضع النافذة
- [ ] منع التمرير والحركة
- [ ] استخدام opacity للإظهار/الإخفاء
- [ ] تحديث تعريفات الرسومات
- [ ] فحص إعدادات النظام
- [ ] اختبار على أجهزة مختلفة

## 🎯 **النتيجة المتوقعة:**
- صفر وميض عند التحميل
- صفر ارتجاج أثناء الاستخدام
- أداء سلس ومستقر
- استجابة فورية للتفاعلات
