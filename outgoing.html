<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المراسلات الصادرة - نظام إدارة المراسلات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-envelope-open-text me-2"></i>
                نظام إدارة المراسلات
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="incoming.html">
                            <i class="fas fa-inbox me-1"></i>
                            المراسلات الواردة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="outgoing.html">
                            <i class="fas fa-paper-plane me-1"></i>
                            المراسلات الصادرة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="entities.html">
                            <i class="fas fa-building me-1"></i>
                            الجهات الخارجية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.html">
                            <i class="fas fa-users me-1"></i>
                            إدارة المستخدمين
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <span id="currentUserName">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><span class="dropdown-item-text">الدور: <span id="currentUserRole">موظف</span></span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="app.logout()">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2 class="card-title text-gradient mb-0">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إدارة المراسلات الصادرة
                                </h2>
                                <p class="card-text text-muted">
                                    إضافة وتعديل وتتبع المراسلات الصادرة إلى الجهات الخارجية
                                </p>
                            </div>
                            <button class="btn btn-success btn-lg" data-bs-toggle="modal" data-bs-target="#correspondenceModal">
                                <i class="fas fa-plus me-2"></i>
                                إضافة مراسلة صادرة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control search-input" id="searchInput" 
                                           placeholder="البحث في المراسلات...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="entityFilter">
                                    <option value="">جميع الجهات</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-secondary w-100" onclick="exportData()">
                                    <i class="fas fa-download me-2"></i>
                                    تصدير البيانات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Correspondence Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة المراسلات الصادرة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="correspondenceTable">
                                <thead>
                                    <tr>
                                        <th>رقم التسجيل</th>
                                        <th>الجهة المستقبلة</th>
                                        <th>تاريخ الإرسال</th>
                                        <th>الموضوع</th>
                                        <th>المرجع الداخلي</th>
                                        <th>المسؤول</th>
                                        <th>مرفقات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="8" class="text-center">جاري التحميل...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <div id="pagination" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div class="modal fade" id="correspondenceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مراسلة صادرة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="correspondenceForm" class="correspondence-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="registrationNumber" class="form-label">رقم التسجيل *</label>
                                <input type="text" class="form-control" id="registrationNumber" name="registrationNumber" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="sendingDate" class="form-label">تاريخ الإرسال *</label>
                                <input type="date" class="form-control" id="sendingDate" name="sendingDate" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="receivingEntity" class="form-label">الجهة المستقبلة *</label>
                                <select class="form-select entity-select" id="receivingEntity" name="receivingEntity" required>
                                    <option value="">اختر الجهة</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="internalReference" class="form-label">المرجع الداخلي</label>
                                <input type="text" class="form-control" id="internalReference" name="internalReference">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="responsiblePerson" class="form-label">المسؤول عن الإعداد</label>
                                <input type="text" class="form-control" id="responsiblePerson" name="responsiblePerson">
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="hasAttachments" name="hasAttachments" value="true">
                                    <label class="form-check-label" for="hasAttachments">
                                        يحتوي على مرفقات
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label">موضوع المراسلة *</label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="saveCorrespondence()">
                        <i class="fas fa-save me-2"></i>
                        حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/database.js"></script>
    <script src="js/activation.js"></script>
    <script src="js/app.js"></script>
    
    <script>
        // Page specific functions
        function saveCorrespondence() {
            const form = document.getElementById('correspondenceForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            // Validate required fields
            if (!data.registrationNumber || !data.sendingDate || !data.receivingEntity || !data.subject) {
                app.showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }
            
            // Set today's date if sending date is empty
            if (!data.sendingDate) {
                data.sendingDate = new Date().toISOString().split('T')[0];
            }
            
            const recordId = form.dataset.recordId;
            
            if (recordId) {
                // Update existing record
                db.updateRecord('outgoing', recordId, data);
                app.showMessage('تم تحديث المراسلة بنجاح', 'success');
            } else {
                // Add new record
                app.addCorrespondence('outgoing', data);
            }
            
            // Close modal and refresh table
            const modal = bootstrap.Modal.getInstance(document.getElementById('correspondenceModal'));
            modal.hide();
            form.reset();
            delete form.dataset.recordId;
            app.loadCorrespondenceTable('outgoing');
        }
        
        function exportData() {
            const data = db.exportTable('outgoing');
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `outgoing_correspondence_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Check system access on page load
        document.addEventListener('DOMContentLoaded', function() {
            if (!checkSystemAccess()) {
                return;
            }
            
            // Set today's date as default
            document.getElementById('sendingDate').value = new Date().toISOString().split('T')[0];
            
            // Load entities for filter
            loadEntitiesForFilter();
        });
        
        function loadEntitiesForFilter() {
            const entities = db.getTable('entities');
            const filter = document.getElementById('entityFilter');
            
            entities.forEach(entity => {
                const option = document.createElement('option');
                option.value = entity.name;
                option.textContent = entity.name;
                filter.appendChild(option);
            });
        }
    </script>
</body>
</html>
