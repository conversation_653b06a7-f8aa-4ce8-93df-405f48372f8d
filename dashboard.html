<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة المراسلات</title>
    <link href="css/style.css" rel="stylesheet">
    <link href="css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- Top Navigation -->
    <div class="top-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="brand-icon">📧</div>
                <div class="brand-text">
                    <div class="brand-title">نظام إدارة المراسلات</div>
                    <div class="brand-subtitle">لوحة التحكم</div>
                </div>
            </div>

            <div class="nav-user">
                <div class="user-info">
                    <div class="user-name" id="currentUserName">المستخدم</div>
                    <div class="user-role" id="currentUserRole">موظف</div>
                </div>
                <div class="user-menu">
                    <button class="user-menu-btn" onclick="toggleUserMenu()">👤</button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" onclick="app.logout()">🚪 تسجيل الخروج</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard -->
    <div class="dashboard-container">
        <!-- Side Navigation -->
        <div class="side-nav">
            <div class="nav-menu">
                <a href="dashboard.html" class="nav-item active">
                    <div class="nav-icon">📊</div>
                    <div class="nav-text">لوحة التحكم</div>
                </a>
                <a href="incoming.html" class="nav-item">
                    <div class="nav-icon">📥</div>
                    <div class="nav-text">المراسلات الواردة</div>
                </a>
                <a href="outgoing.html" class="nav-item">
                    <div class="nav-icon">📤</div>
                    <div class="nav-text">المراسلات الصادرة</div>
                </a>
                <a href="entities.html" class="nav-item">
                    <div class="nav-icon">🏢</div>
                    <div class="nav-text">الجهات الخارجية</div>
                </a>
                <a href="users.html" class="nav-item">
                    <div class="nav-icon">👥</div>
                    <div class="nav-text">إدارة المستخدمين</div>
                </a>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Welcome Section -->
            <div class="welcome-section">
                <div class="welcome-card">
                    <div class="welcome-icon">🏠</div>
                    <div class="welcome-text">
                        <h1>مرحباً بك في نظام إدارة المراسلات الإدارية</h1>
                        <p>نظام شامل لإدارة المراسلات الواردة والصادرة مع إمكانيات متقدمة للتتبع والأرشفة</p>
                    </div>
                </div>
            </div>

            <!-- Statistics Grid -->
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon">📥</div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalIncoming">0</div>
                        <div class="stat-label">إجمالي المراسلات الواردة</div>
                    </div>
                </div>

                <div class="stat-card success">
                    <div class="stat-icon">📤</div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalOutgoing">0</div>
                        <div class="stat-label">إجمالي المراسلات الصادرة</div>
                    </div>
                </div>

                <div class="stat-card warning">
                    <div class="stat-icon">⏰</div>
                    <div class="stat-content">
                        <div class="stat-number" id="pendingIncoming">0</div>
                        <div class="stat-label">المراسلات المعلقة</div>
                    </div>
                </div>

                <div class="stat-card info">
                    <div class="stat-icon">🏢</div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalEntities">0</div>
                        <div class="stat-label">الجهات الخارجية</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="actions-section">
                <div class="section-header">
                    <div class="section-icon">⚡</div>
                    <div class="section-title">إجراءات سريعة</div>
                </div>
                <div class="actions-grid">
                    <a href="incoming.html" class="action-card primary">
                        <div class="action-icon">➕</div>
                        <div class="action-text">إضافة مراسلة واردة</div>
                    </a>
                    <a href="outgoing.html" class="action-card success">
                        <div class="action-icon">📤</div>
                        <div class="action-text">إضافة مراسلة صادرة</div>
                    </a>
                    <a href="entities.html" class="action-card info">
                        <div class="action-icon">🏢</div>
                        <div class="action-text">إضافة جهة خارجية</div>
                    </a>
                    <button class="action-card warning" onclick="generateReport()">
                        <div class="action-icon">📊</div>
                        <div class="action-text">تقرير شامل</div>
                    </button>
                </div>
            </div>

            <!-- Recent Correspondence -->
            <div class="tables-section">
                <div class="tables-grid">
                    <div class="table-card">
                        <div class="table-header">
                            <div class="table-title">
                                <div class="table-icon">📥</div>
                                <span>أحدث المراسلات الواردة</span>
                            </div>
                            <a href="incoming.html" class="view-all-btn">عرض الكل</a>
                        </div>
                        <div class="table-content">
                            <div class="simple-table" id="recentIncomingTable">
                                <div class="table-row header">
                                    <div class="table-cell">رقم التسجيل</div>
                                    <div class="table-cell">الجهة المرسلة</div>
                                    <div class="table-cell">تاريخ الاستلام</div>
                                    <div class="table-cell">الموضوع</div>
                                </div>
                                <div class="table-row">
                                    <div class="table-cell loading" colspan="4">جاري التحميل...</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-card">
                        <div class="table-header">
                            <div class="table-title">
                                <div class="table-icon">📤</div>
                                <span>أحدث المراسلات الصادرة</span>
                            </div>
                            <a href="outgoing.html" class="view-all-btn">عرض الكل</a>
                        </div>
                        <div class="table-content">
                            <div class="simple-table" id="recentOutgoingTable">
                                <div class="table-row header">
                                    <div class="table-cell">رقم التسجيل</div>
                                    <div class="table-cell">الجهة المستقبلة</div>
                                    <div class="table-cell">تاريخ الإرسال</div>
                                    <div class="table-cell">الموضوع</div>
                                </div>
                                <div class="table-row">
                                    <div class="table-cell loading" colspan="4">جاري التحميل...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script src="js/activation.js"></script>
    <script src="js/app.js"></script>

    <script>
        // Dashboard specific functions
        function generateReport() {
            const stats = db.getStatistics();
            const reportData = {
                generatedDate: new Date().toISOString(),
                statistics: stats,
                user: db.getCurrentUser()
            };

            // Create a simple report
            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(`
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير النظام</title>
                    <style>
                        body { font-family: 'Cairo', Arial, sans-serif; margin: 20px; direction: rtl; background: white; }
                        .header { text-align: center; border-bottom: 2px solid #1a365d; padding-bottom: 20px; margin-bottom: 30px; }
                        .header h1 { color: #1a365d; margin-bottom: 10px; }
                        .stats { margin: 20px 0; }
                        .stat-item { margin: 15px 0; padding: 15px; background: #f9fafb; border-radius: 8px; border-right: 4px solid #1a365d; }
                        .stat-item strong { color: #1a365d; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>📊 تقرير نظام إدارة المراسلات</h1>
                        <p><strong>تاريخ التقرير:</strong> ${app.formatDate(reportData.generatedDate)}</p>
                        <p><strong>المستخدم:</strong> ${reportData.user.fullName}</p>
                    </div>
                    <div class="stats">
                        <h2>📈 الإحصائيات العامة</h2>
                        <div class="stat-item"><strong>📥 إجمالي المراسلات الواردة:</strong> ${stats.totalIncoming}</div>
                        <div class="stat-item"><strong>📤 إجمالي المراسلات الصادرة:</strong> ${stats.totalOutgoing}</div>
                        <div class="stat-item"><strong>⏰ المراسلات المعلقة:</strong> ${stats.pendingIncoming}</div>
                        <div class="stat-item"><strong>🏢 الجهات الخارجية:</strong> ${stats.totalEntities}</div>
                        <div class="stat-item"><strong>👥 المستخدمين:</strong> ${stats.totalUsers}</div>
                        <div class="stat-item"><strong>📅 المراسلات الواردة هذا الشهر:</strong> ${stats.thisMonthIncoming}</div>
                        <div class="stat-item"><strong>📅 المراسلات الصادرة هذا الشهر:</strong> ${stats.thisMonthOutgoing}</div>
                    </div>
                </body>
                </html>
            `);
            reportWindow.document.close();
        }

        // Toggle user menu
        function toggleUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        }

        // Close user menu when clicking outside
        document.addEventListener('click', function(e) {
            const userMenu = document.querySelector('.user-menu');
            const dropdown = document.getElementById('userDropdown');
            if (!userMenu.contains(e.target)) {
                dropdown.style.display = 'none';
            }
        });

        // Check system access on page load
        document.addEventListener('DOMContentLoaded', function() {
            if (!checkSystemAccess()) {
                return;
            }
        });
    </script>
</body>
</html>
