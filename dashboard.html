<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة المراسلات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-envelope-open-text me-2"></i>
                نظام إدارة المراسلات
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.html">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="incoming.html">
                            <i class="fas fa-inbox me-1"></i>
                            المراسلات الواردة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="outgoing.html">
                            <i class="fas fa-paper-plane me-1"></i>
                            المراسلات الصادرة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="entities.html">
                            <i class="fas fa-building me-1"></i>
                            الجهات الخارجية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.html">
                            <i class="fas fa-users me-1"></i>
                            إدارة المستخدمين
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <span id="currentUserName">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><span class="dropdown-item-text">الدور: <span id="currentUserRole">موظف</span></span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="app.logout()">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h2 class="card-title text-gradient">
                            <i class="fas fa-home me-2"></i>
                            مرحباً بك في نظام إدارة المراسلات الإدارية
                        </h2>
                        <p class="card-text text-muted">
                            نظام شامل لإدارة المراسلات الواردة والصادرة مع إمكانيات متقدمة للتتبع والأرشفة
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="card-title mb-0" id="totalIncoming">0</h3>
                                <p class="card-text">إجمالي المراسلات الواردة</p>
                            </div>
                            <div class="fs-1">
                                <i class="fas fa-inbox"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="card-title mb-0" id="totalOutgoing">0</h3>
                                <p class="card-text">إجمالي المراسلات الصادرة</p>
                            </div>
                            <div class="fs-1">
                                <i class="fas fa-paper-plane"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="card-title mb-0" id="pendingIncoming">0</h3>
                                <p class="card-text">المراسلات المعلقة</p>
                            </div>
                            <div class="fs-1">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="card-title mb-0" id="totalEntities">0</h3>
                                <p class="card-text">الجهات الخارجية</p>
                            </div>
                            <div class="fs-1">
                                <i class="fas fa-building"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="incoming.html" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-plus-circle fs-2 mb-2"></i>
                                    <span>إضافة مراسلة واردة</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="outgoing.html" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-share fs-2 mb-2"></i>
                                    <span>إضافة مراسلة صادرة</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="entities.html" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-building fs-2 mb-2"></i>
                                    <span>إضافة جهة خارجية</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <button class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center" onclick="generateReport()">
                                    <i class="fas fa-chart-bar fs-2 mb-2"></i>
                                    <span>تقرير شامل</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Correspondence -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-inbox me-2"></i>
                            أحدث المراسلات الواردة
                        </h5>
                        <a href="incoming.html" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="recentIncomingTable">
                                <thead>
                                    <tr>
                                        <th>رقم التسجيل</th>
                                        <th>الجهة المرسلة</th>
                                        <th>تاريخ الاستلام</th>
                                        <th>الموضوع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="4" class="text-center">جاري التحميل...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-paper-plane me-2"></i>
                            أحدث المراسلات الصادرة
                        </h5>
                        <a href="outgoing.html" class="btn btn-sm btn-outline-success">عرض الكل</a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="recentOutgoingTable">
                                <thead>
                                    <tr>
                                        <th>رقم التسجيل</th>
                                        <th>الجهة المستقبلة</th>
                                        <th>تاريخ الإرسال</th>
                                        <th>الموضوع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="4" class="text-center">جاري التحميل...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/database.js"></script>
    <script src="js/activation.js"></script>
    <script src="js/app.js"></script>
    
    <script>
        // Dashboard specific functions
        function generateReport() {
            const stats = db.getStatistics();
            const reportData = {
                generatedDate: new Date().toISOString(),
                statistics: stats,
                user: db.getCurrentUser()
            };
            
            // Create a simple report
            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(`
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير النظام</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; }
                        .stats { margin: 20px 0; }
                        .stat-item { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 5px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>تقرير نظام إدارة المراسلات</h1>
                        <p>تاريخ التقرير: ${app.formatDate(reportData.generatedDate)}</p>
                        <p>المستخدم: ${reportData.user.fullName}</p>
                    </div>
                    <div class="stats">
                        <h2>الإحصائيات العامة</h2>
                        <div class="stat-item">إجمالي المراسلات الواردة: ${stats.totalIncoming}</div>
                        <div class="stat-item">إجمالي المراسلات الصادرة: ${stats.totalOutgoing}</div>
                        <div class="stat-item">المراسلات المعلقة: ${stats.pendingIncoming}</div>
                        <div class="stat-item">الجهات الخارجية: ${stats.totalEntities}</div>
                        <div class="stat-item">المستخدمين: ${stats.totalUsers}</div>
                        <div class="stat-item">المراسلات الواردة هذا الشهر: ${stats.thisMonthIncoming}</div>
                        <div class="stat-item">المراسلات الصادرة هذا الشهر: ${stats.thisMonthOutgoing}</div>
                    </div>
                </body>
                </html>
            `);
            reportWindow.document.close();
        }

        // Check system access on page load
        document.addEventListener('DOMContentLoaded', function() {
            if (!checkSystemAccess()) {
                return;
            }
        });
    </script>
</body>
</html>
