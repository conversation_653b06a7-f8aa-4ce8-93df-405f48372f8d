// حل مشكلة الارتجاج في Electron
const { BrowserWindow, app } = require('electron');

function createWindow() {
    const mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        
        // إعدادات منع الارتجاج
        show: false, // لا تظهر النافذة حتى تكتمل
        backgroundColor: '#1a365d', // لون خلفية ثابت
        
        webPreferences: {
            // تحسينات الأداء
            hardwareAcceleration: false, // إيقاف تسريع الأجهزة
            enableRemoteModule: false,
            contextIsolation: true,
            nodeIntegration: false,
            
            // منع الوميض
            offscreen: false,
            paintWhenInitiallyHidden: false,
            
            // تحسين الذاكرة
            v8CacheOptions: 'code',
            enableWebSQL: false
        },
        
        // إعدادات النافذة
        resizable: false, // منع تغيير الحجم
        minimizable: true,
        maximizable: false,
        fullscreenable: false,
        
        // إعدادات العرض
        transparent: false, // منع الشفافية
        frame: true, // إطار النافذة
        titleBarStyle: 'default',
        
        // منع الوميض عند التحميل
        webSecurity: true,
        allowRunningInsecureContent: false
    });

    // تحميل الملف
    mainWindow.loadFile('index.html');

    // إظهار النافذة بعد التحميل الكامل
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // منع الوميض الإضافي
        mainWindow.webContents.once('dom-ready', () => {
            // إضافة CSS لمنع الوميض
            mainWindow.webContents.insertCSS(`
                body {
                    opacity: 1 !important;
                    visibility: visible !important;
                }
                
                * {
                    -webkit-backface-visibility: hidden !important;
                    -webkit-transform: translateZ(0) !important;
                }
            `);
        });
    });

    // منع إعادة التحميل
    mainWindow.webContents.on('before-input-event', (event, input) => {
        if (input.control && input.key.toLowerCase() === 'r') {
            event.preventDefault();
        }
        if (input.key === 'F5') {
            event.preventDefault();
        }
    });

    return mainWindow;
}

app.whenReady().then(createWindow);

// إعدادات إضافية لمنع الوميض
app.commandLine.appendSwitch('disable-features', 'VizDisplayCompositor');
app.commandLine.appendSwitch('disable-gpu-vsync');
app.commandLine.appendSwitch('disable-gpu-compositing');
app.commandLine.appendSwitch('disable-software-rasterizer');
