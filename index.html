<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المراسلات الإدارية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div class="row w-100">
            <div class="col-md-6 col-lg-4 mx-auto">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-envelope-open-text me-2"></i>
                            نظام إدارة المراسلات الإدارية
                        </h3>
                    </div>
                    <div class="card-body p-5">
                        <!-- Activation Status -->
                        <div id="activationStatus" class="alert alert-info text-center mb-4">
                            <i class="fas fa-clock me-2"></i>
                            <span id="trialDaysLeft">3</span> أيام متبقية من فترة التجربة
                        </div>

                        <!-- Activation Form -->
                        <div id="activationForm">
                            <div class="mb-4">
                                <label for="activationCode" class="form-label">
                                    <i class="fas fa-key me-2"></i>
                                    رمز التفعيل
                                </label>
                                <input type="text" class="form-control form-control-lg text-center" 
                                       id="activationCode" placeholder="أدخل رمز التفعيل">
                                <div class="form-text text-center">
                                    أدخل رمز التفعيل للوصول الكامل للنظام
                                </div>
                            </div>
                            <button type="button" class="btn btn-success btn-lg w-100 mb-3" onclick="activateSystem()">
                                <i class="fas fa-check me-2"></i>
                                تفعيل النظام
                            </button>
                        </div>

                        <!-- Trial Access Button -->
                        <div id="trialAccess">
                            <button type="button" class="btn btn-primary btn-lg w-100 mb-3" onclick="enterTrialMode()">
                                <i class="fas fa-play me-2"></i>
                                دخول النظام (فترة تجربة)
                            </button>
                        </div>

                        <!-- Blocked Access Message -->
                        <div id="blockedAccess" class="alert alert-danger text-center" style="display: none;">
                            <i class="fas fa-lock me-2"></i>
                            <h5>مطلوب تفعيل النظام</h5>
                            <p class="mb-0">انتهت فترة التجربة. يرجى إدخال رمز التفعيل للمتابعة.</p>
                        </div>

                        <hr class="my-4">
                        
                        <!-- System Info -->
                        <div class="text-center text-muted">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                نظام إدارة المراسلات الواردة والصادرة
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/database.js"></script>
    <script src="js/activation.js"></script>
    <script src="js/app.js"></script>
    
    <script>
        // Initialize activation system on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeActivationSystem();
        });
    </script>
</body>
</html>
