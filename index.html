<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المراسلات الإدارية</title>
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="main-container">
        <div class="activation-box">
            <div class="header-section">
                <div class="logo-icon">📧</div>
                <h1 class="system-title">نظام إدارة المراسلات الإدارية</h1>
                <p class="system-subtitle">إدارة المراسلات الواردة والصادرة</p>
            </div>
            <!-- Status Section -->
            <div class="status-section">
                <div id="activationStatus" class="status-box info">
                    <div class="status-icon">⏰</div>
                    <div class="status-text">
                        <span id="trialDaysLeft">3</span> أيام متبقية من فترة التجربة
                    </div>
                </div>
            </div>

            <!-- Activation Form -->
            <div id="activationForm" class="form-section">
                <div class="input-group">
                    <label for="activationCode" class="input-label">
                        🔑 رمز التفعيل
                    </label>
                    <input type="text" id="activationCode" class="text-input" placeholder="أدخل رمز التفعيل">
                    <div class="input-help">أدخل رمز التفعيل للوصول الكامل للنظام</div>
                </div>
                <button type="button" class="action-btn success" onclick="activateSystem()">
                    ✅ تفعيل النظام
                </button>
            </div>

            <!-- Trial Access -->
            <div id="trialAccess" class="form-section">
                <button type="button" class="action-btn primary" onclick="enterTrialMode()">
                    ▶️ دخول النظام (فترة تجربة)
                </button>
            </div>

            <!-- Blocked Access -->
            <div id="blockedAccess" class="status-box error" style="display: none;">
                <div class="status-icon">🔒</div>
                <div class="status-text">
                    <strong>مطلوب تفعيل النظام</strong><br>
                    انتهت فترة التجربة. يرجى إدخال رمز التفعيل للمتابعة.
                </div>
            </div>

            <!-- Footer Info -->
            <div class="footer-section">
                <div class="system-info">
                    ℹ️ نظام إدارة المراسلات الواردة والصادرة
                </div>
                <div id="securityInfo" class="security-info" style="display: none;">
                    🛡️ نظام حماية متقدم ضد التلاعب
                </div>
                <div id="devModeInfo" class="dev-info" style="display: none;">
                    💻 وضع التطوير: افتح وحدة تحكم المطور (F12) لرؤية معلومات إضافية
                </div>
            </div>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script src="js/activation.js"></script>
    <script src="js/app.js"></script>

    <script>
        // Initialize activation system on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent multiple initializations
            if (window.activationInitialized) return;
            window.activationInitialized = true;

            initializeActivationSystem();

            // Show development mode info if applicable
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                const devInfo = document.getElementById('devModeInfo');
                if (devInfo) devInfo.style.display = 'block';
            }

            // Show security info after a delay
            setTimeout(() => {
                const secInfo = document.getElementById('securityInfo');
                if (secInfo) secInfo.style.display = 'block';
            }, 2000);
        });

        // Prevent any form submission
        document.addEventListener('submit', function(e) {
            e.preventDefault();
        });

        // Prevent context menu and selection
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // Disable text selection
        document.addEventListener('selectstart', function(e) {
            e.preventDefault();
        });
    </script>
</body>
</html>
