// Activation System for Administrative Correspondence Management
class ActivationSystem {
    constructor() {
        this.trialPeriodDays = 3;
        this.validActivationCodes = [
            'ADMIN2024',
            'CORR-MGMT-2024',
            'ACTIVATE-SYS',
            'DEPT-ARRIVE-2024'
        ];
        this.init();
    }

    init() {
        this.checkTrialStatus();
        this.updateUI();
    }

    // Check if trial period has started and calculate remaining days
    checkTrialStatus() {
        const trialStartDate = localStorage.getItem('trialStartDate');
        const isActivated = localStorage.getItem('systemActivated');
        
        if (isActivated === 'true') {
            this.isActivated = true;
            this.trialExpired = false;
            return;
        }

        if (!trialStartDate) {
            // First time user - trial hasn't started yet
            this.trialStarted = false;
            this.trialExpired = false;
            this.remainingDays = this.trialPeriodDays;
        } else {
            // Trial has started - calculate remaining days
            this.trialStarted = true;
            const startDate = new Date(trialStartDate);
            const currentDate = new Date();
            const daysPassed = Math.floor((currentDate - startDate) / (1000 * 60 * 60 * 24));
            
            this.remainingDays = Math.max(0, this.trialPeriodDays - daysPassed);
            this.trialExpired = this.remainingDays <= 0;
        }
    }

    // Start trial period
    startTrial() {
        const currentDate = new Date();
        localStorage.setItem('trialStartDate', currentDate.toISOString());
        this.trialStarted = true;
        this.checkTrialStatus();
        this.updateUI();
    }

    // Activate system with code
    activateSystem(code) {
        if (this.validActivationCodes.includes(code.toUpperCase())) {
            localStorage.setItem('systemActivated', 'true');
            localStorage.setItem('activationDate', new Date().toISOString());
            this.isActivated = true;
            this.showSuccessMessage('تم تفعيل النظام بنجاح!');
            setTimeout(() => {
                this.redirectToLogin();
            }, 2000);
            return true;
        } else {
            this.showErrorMessage('رمز التفعيل غير صحيح. يرجى المحاولة مرة أخرى.');
            return false;
        }
    }

    // Update UI based on activation status
    updateUI() {
        const trialDaysElement = document.getElementById('trialDaysLeft');
        const activationStatus = document.getElementById('activationStatus');
        const activationForm = document.getElementById('activationForm');
        const trialAccess = document.getElementById('trialAccess');
        const blockedAccess = document.getElementById('blockedAccess');

        if (this.isActivated) {
            // System is activated - redirect to login
            this.redirectToLogin();
            return;
        }

        if (trialDaysElement) {
            trialDaysElement.textContent = this.remainingDays;
        }

        if (this.trialExpired) {
            // Trial expired - show only activation form
            activationStatus.style.display = 'none';
            trialAccess.style.display = 'none';
            blockedAccess.style.display = 'block';
        } else {
            // Trial active or not started
            activationStatus.style.display = 'block';
            trialAccess.style.display = 'block';
            blockedAccess.style.display = 'none';
            
            if (this.remainingDays === 0) {
                activationStatus.className = 'alert alert-danger text-center mb-4';
                activationStatus.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>آخر يوم من فترة التجربة!';
            } else if (this.remainingDays === 1) {
                activationStatus.className = 'alert alert-warning text-center mb-4';
                activationStatus.innerHTML = '<i class="fas fa-clock me-2"></i>يوم واحد متبقي من فترة التجربة';
            }
        }
    }

    // Enter trial mode
    enterTrialMode() {
        if (this.trialExpired) {
            this.showErrorMessage('انتهت فترة التجربة. يرجى إدخال رمز التفعيل.');
            return false;
        }

        if (!this.trialStarted) {
            this.startTrial();
        }

        // Redirect to login page
        this.redirectToLogin();
        return true;
    }

    // Check if user can access the system
    canAccessSystem() {
        return this.isActivated || (!this.trialExpired && this.trialStarted);
    }

    // Get system status for display
    getSystemStatus() {
        if (this.isActivated) {
            return {
                status: 'activated',
                message: 'النظام مفعل',
                class: 'success'
            };
        } else if (this.trialExpired) {
            return {
                status: 'expired',
                message: 'انتهت فترة التجربة',
                class: 'danger'
            };
        } else if (this.trialStarted) {
            return {
                status: 'trial',
                message: `${this.remainingDays} أيام متبقية`,
                class: this.remainingDays <= 1 ? 'warning' : 'info'
            };
        } else {
            return {
                status: 'not_started',
                message: 'لم تبدأ فترة التجربة',
                class: 'info'
            };
        }
    }

    // Utility methods
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }

    showErrorMessage(message) {
        this.showMessage(message, 'danger');
    }

    showMessage(message, type) {
        // Remove existing messages
        const existingMessages = document.querySelectorAll('.activation-message');
        existingMessages.forEach(msg => msg.remove());

        // Create new message
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type} activation-message fade-in`;
        messageDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'} me-2"></i>${message}`;
        
        // Insert message
        const form = document.getElementById('activationForm');
        form.parentNode.insertBefore(messageDiv, form);

        // Auto remove after 5 seconds
        setTimeout(() => {
            messageDiv.remove();
        }, 5000);
    }

    redirectToLogin() {
        window.location.href = 'login.html';
    }

    // Format date in Latin format (YYYY-MM-DD)
    formatDate(date) {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    // Get activation info for admin panel
    getActivationInfo() {
        return {
            isActivated: this.isActivated,
            trialStarted: this.trialStarted,
            trialExpired: this.trialExpired,
            remainingDays: this.remainingDays,
            trialStartDate: localStorage.getItem('trialStartDate'),
            activationDate: localStorage.getItem('activationDate')
        };
    }
}

// Global activation system instance
let activationSystem;

// Initialize activation system
function initializeActivationSystem() {
    activationSystem = new ActivationSystem();
}

// Global functions for HTML onclick events
function activateSystem() {
    const codeInput = document.getElementById('activationCode');
    const code = codeInput.value.trim();
    
    if (!code) {
        activationSystem.showErrorMessage('يرجى إدخال رمز التفعيل');
        return;
    }
    
    activationSystem.activateSystem(code);
}

function enterTrialMode() {
    activationSystem.enterTrialMode();
}

// Check access on protected pages
function checkSystemAccess() {
    if (!activationSystem) {
        activationSystem = new ActivationSystem();
    }
    
    if (!activationSystem.canAccessSystem()) {
        window.location.href = 'index.html';
        return false;
    }
    
    return true;
}
