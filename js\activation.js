// Activation System for Administrative Correspondence Management
class ActivationSystem {
    constructor() {
        this.trialPeriodDays = 3;
        this.maxTrialSessions = 10; // Maximum number of sessions during trial
        this.validActivationCodes = [
            'ADMIN2024',
            'CORR-MGMT-2024',
            'ACTIVATE-SYS',
            'DEPT-ARRIVE-2024'
        ];
        this.securityKey = 'CORR_SYS_2024'; // Security key for data integrity
        this.init();
    }

    init() {
        this.isUpdating = false; // Flag to prevent multiple updates
        this.lastUIState = null; // Track last UI state
        this.preventMovement(); // Prevent any movement
        this.checkTrialStatus();

        // Single UI update without delay
        this.updateUI();
        this.recordSession();
    }

    // Prevent any movement or animation
    preventMovement() {
        // Disable all CSS transitions and animations
        const style = document.createElement('style');
        style.textContent = `
            *, *::before, *::after {
                transition: none !important;
                animation: none !important;
                transform: translate3d(0,0,0) !important;
                -webkit-transform: translate3d(0,0,0) !important;
                backface-visibility: hidden !important;
                -webkit-backface-visibility: hidden !important;
            }
        `;
        document.head.appendChild(style);

        // Prevent scroll events
        document.addEventListener('scroll', (e) => {
            e.preventDefault();
            window.scrollTo(0, 0);
        }, { passive: false });

        // Prevent resize events
        window.addEventListener('resize', (e) => {
            e.preventDefault();
        }, { passive: false });

        // Prevent touch events that might cause movement
        document.addEventListener('touchmove', (e) => {
            e.preventDefault();
        }, { passive: false });
    }

    // Enhanced trial status check with anti-tampering measures
    checkTrialStatus() {
        const isActivated = this.getSecureData('systemActivated');

        if (isActivated === 'true') {
            this.isActivated = true;
            this.trialExpired = false;
            return;
        }

        const trialData = this.getTrialData();

        if (!trialData.started) {
            // First time user - trial hasn't started yet
            this.trialStarted = false;
            this.trialExpired = false;
            this.remainingDays = this.trialPeriodDays;
        } else {
            // Trial has started - use multiple validation methods
            this.trialStarted = true;
            this.remainingDays = this.calculateRemainingDays(trialData);
            this.trialExpired = this.remainingDays <= 0 || this.isTrialTampered(trialData);
        }
    }

    // Get trial data with integrity check
    getTrialData() {
        try {
            const encryptedData = localStorage.getItem('trialInfo');
            if (!encryptedData) {
                return { started: false, sessions: 0, timestamps: [] };
            }

            const data = this.decryptData(encryptedData);

            // Validate data integrity
            if (!this.validateTrialData(data)) {
                console.warn('Trial data integrity compromised, resetting...');
                this.resetTrialData();
                return { started: false, sessions: 0, timestamps: [] };
            }

            return data;
        } catch (error) {
            console.warn('Error reading trial data, resetting...');
            this.resetTrialData();
            return { started: false, sessions: 0, timestamps: [] };
        }
    }

    // Calculate remaining days using multiple methods
    calculateRemainingDays(trialData) {
        const now = Date.now();
        const timestamps = trialData.timestamps || [];

        if (timestamps.length === 0) {
            return this.trialPeriodDays;
        }

        // Method 1: Time-based calculation
        const firstTimestamp = Math.min(...timestamps);
        const daysPassed = Math.floor((now - firstTimestamp) / (1000 * 60 * 60 * 24));
        const remainingByTime = Math.max(0, this.trialPeriodDays - daysPassed);

        // Method 2: Session-based calculation
        const sessionProgress = trialData.sessions / this.maxTrialSessions;
        const remainingBySessions = Math.max(0, this.trialPeriodDays - Math.floor(sessionProgress * this.trialPeriodDays));

        // Method 3: Timestamp consistency check
        const validTimestamps = this.validateTimestamps(timestamps);
        if (!validTimestamps) {
            return 0; // Force expiration if timestamps are invalid
        }

        // Use the most restrictive calculation
        return Math.min(remainingByTime, remainingBySessions);
    }

    // Validate timestamps for consistency
    validateTimestamps(timestamps) {
        if (timestamps.length < 2) return true;

        // Check if timestamps are in chronological order
        for (let i = 1; i < timestamps.length; i++) {
            if (timestamps[i] < timestamps[i-1]) {
                return false; // Timestamps out of order - possible tampering
            }

            // Check for unrealistic time jumps (more than 24 hours between sessions)
            const timeDiff = timestamps[i] - timestamps[i-1];
            if (timeDiff > 24 * 60 * 60 * 1000) {
                // Allow but log suspicious activity
                console.warn('Large time gap detected between sessions');
            }
        }

        // Check if any timestamp is in the future
        const now = Date.now();
        return timestamps.every(ts => ts <= now + 60000); // Allow 1 minute tolerance
    }

    // Check if trial data has been tampered with
    isTrialTampered(trialData) {
        // Check for impossible session counts
        if (trialData.sessions > this.maxTrialSessions * 2) {
            return true;
        }

        // Check timestamp consistency
        if (!this.validateTimestamps(trialData.timestamps)) {
            return true;
        }

        // Check for data structure integrity
        if (!trialData.hasOwnProperty('started') ||
            !trialData.hasOwnProperty('sessions') ||
            !trialData.hasOwnProperty('timestamps')) {
            return true;
        }

        return false;
    }

    // Record current session
    recordSession() {
        const trialData = this.getTrialData();
        const now = Date.now();

        // Add current timestamp
        trialData.timestamps = trialData.timestamps || [];
        trialData.timestamps.push(now);

        // Keep only last 20 timestamps to prevent data bloat
        if (trialData.timestamps.length > 20) {
            trialData.timestamps = trialData.timestamps.slice(-20);
        }

        // Increment session count
        trialData.sessions = (trialData.sessions || 0) + 1;
        trialData.started = true;

        // Save encrypted data
        this.saveTrialData(trialData);
    }

    // Start trial period with enhanced security
    startTrial() {
        const trialData = this.getTrialData();
        if (!trialData.started) {
            trialData.started = true;
            trialData.sessions = 1;
            trialData.timestamps = [Date.now()];
            this.saveTrialData(trialData);
        }

        this.trialStarted = true;
        this.checkTrialStatus();

        // Update UI only once
        if (!this.isUpdating) {
            this.updateUI();
        }
    }

    // Simple encryption for data protection
    encryptData(data) {
        try {
            const jsonString = JSON.stringify(data);
            const encoded = btoa(jsonString + '|' + this.securityKey);
            return encoded;
        } catch (error) {
            console.error('Encryption error:', error);
            return null;
        }
    }

    // Simple decryption for data protection
    decryptData(encryptedData) {
        try {
            const decoded = atob(encryptedData);
            const parts = decoded.split('|');

            if (parts.length !== 2 || parts[1] !== this.securityKey) {
                throw new Error('Invalid security key');
            }

            return JSON.parse(parts[0]);
        } catch (error) {
            console.error('Decryption error:', error);
            throw error;
        }
    }

    // Save trial data securely
    saveTrialData(data) {
        const encrypted = this.encryptData(data);
        if (encrypted) {
            localStorage.setItem('trialInfo', encrypted);
        }
    }

    // Reset trial data
    resetTrialData() {
        localStorage.removeItem('trialInfo');
    }

    // Validate trial data structure
    validateTrialData(data) {
        return data &&
               typeof data.started === 'boolean' &&
               typeof data.sessions === 'number' &&
               Array.isArray(data.timestamps) &&
               data.sessions >= 0 &&
               data.sessions <= this.maxTrialSessions * 3; // Allow some flexibility
    }

    // Get secure data with integrity check
    getSecureData(key) {
        try {
            const encryptedData = localStorage.getItem(key + '_secure');
            if (!encryptedData) {
                return localStorage.getItem(key); // Fallback to regular storage
            }

            const data = this.decryptData(encryptedData);
            return data.value;
        } catch (error) {
            return localStorage.getItem(key); // Fallback to regular storage
        }
    }

    // Set secure data with encryption
    setSecureData(key, value) {
        try {
            const data = { value: value, timestamp: Date.now() };
            const encrypted = this.encryptData(data);
            if (encrypted) {
                localStorage.setItem(key + '_secure', encrypted);
            }
            // Also store in regular format as backup
            localStorage.setItem(key, value);
        } catch (error) {
            // Fallback to regular storage
            localStorage.setItem(key, value);
        }
    }

    // Activate system with code
    activateSystem(code) {
        if (this.validActivationCodes.includes(code.toUpperCase())) {
            this.setSecureData('systemActivated', 'true');
            this.setSecureData('activationDate', new Date().toISOString());
            this.isActivated = true;
            this.showSuccessMessage('تم تفعيل النظام بنجاح!');
            setTimeout(() => {
                this.redirectToLogin();
            }, 2000);
            return true;
        } else {
            this.showErrorMessage('رمز التفعيل غير صحيح. يرجى المحاولة مرة أخرى.');
            return false;
        }
    }

    // Update UI based on activation status
    updateUI() {
        // Prevent multiple simultaneous updates
        if (this.isUpdating) return;
        this.isUpdating = true;

        try {
            // Create current state hash to check if update is needed
            const currentState = {
                isActivated: this.isActivated,
                trialExpired: this.trialExpired,
                remainingDays: this.remainingDays,
                trialStarted: this.trialStarted
            };

            const stateHash = JSON.stringify(currentState);
            if (this.lastUIState === stateHash) {
                this.isUpdating = false;
                return; // No change needed
            }

            this.lastUIState = stateHash;

            const trialDaysElement = document.getElementById('trialDaysLeft');
            const activationStatus = document.getElementById('activationStatus');
            const activationForm = document.getElementById('activationForm');
            const trialAccess = document.getElementById('trialAccess');
            const blockedAccess = document.getElementById('blockedAccess');

            if (this.isActivated) {
                // System is activated - redirect to login immediately
                this.redirectToLogin();
                return;
            }

        // Only update if content actually changed
        if (trialDaysElement && trialDaysElement.textContent !== this.remainingDays.toString()) {
            trialDaysElement.textContent = this.remainingDays;
        }

        // Get trial data for additional info
        const trialData = this.getTrialData();
        const sessionInfo = trialData.sessions ? ` (جلسة ${trialData.sessions})` : '';

        if (this.trialExpired) {
            // Trial expired - show only activation form
            if (activationStatus && activationStatus.style.display !== 'none') {
                activationStatus.style.display = 'none';
            }
            if (trialAccess && trialAccess.style.display !== 'none') {
                trialAccess.style.display = 'none';
            }
            if (blockedAccess && blockedAccess.style.display !== 'block') {
                blockedAccess.style.display = 'block';
                // Show reason for expiration
                const reason = this.isTrialTampered(trialData) ?
                    'تم اكتشاف محاولة تلاعب بالنظام' :
                    'انتهت فترة التجربة';
                blockedAccess.innerHTML = `
                    <div>
                        <i class="fas fa-lock me-2"></i>
                        <h5>مطلوب تفعيل النظام</h5>
                        <p class="mb-0">${reason}. يرجى إدخال رمز التفعيل للمتابعة.</p>
                    </div>
                `;
            }
        } else {
            // Trial active or not started
            if (activationStatus && activationStatus.style.display !== 'block') {
                activationStatus.style.display = 'block';
            }
            if (trialAccess && trialAccess.style.display !== 'block') {
                trialAccess.style.display = 'block';
            }
            if (blockedAccess && blockedAccess.style.display !== 'none') {
                blockedAccess.style.display = 'none';
            }

            // Only update status content if it changed
            if (activationStatus) {
                let newClass = '';
                let newIcon = '';
                let newText = '';

                if (this.remainingDays === 0) {
                    newClass = 'status-box error';
                    newIcon = '⚠️';
                    newText = `آخر يوم من فترة التجربة!${sessionInfo}`;
                } else if (this.remainingDays === 1) {
                    newClass = 'status-box warning';
                    newIcon = '⏰';
                    newText = `يوم واحد متبقي من فترة التجربة${sessionInfo}`;
                } else {
                    newClass = 'status-box info';
                    newIcon = '⏰';
                    newText = `<span id="trialDaysLeft">${this.remainingDays}</span> أيام متبقية من فترة التجربة${sessionInfo}`;
                }

                if (activationStatus.className !== newClass) {
                    activationStatus.className = newClass;
                }

                const statusIcon = activationStatus.querySelector('.status-icon');
                const statusText = activationStatus.querySelector('.status-text');

                if (statusIcon && statusIcon.textContent !== newIcon) {
                    statusIcon.textContent = newIcon;
                }

                if (statusText && statusText.innerHTML !== newText) {
                    statusText.innerHTML = newText;
                }
            }
        }
        } catch (error) {
            console.error('Error updating UI:', error);
        } finally {
            // Reset the updating flag after a short delay
            setTimeout(() => {
                this.isUpdating = false;
            }, 100);
        }
    }

    // Enter trial mode
    enterTrialMode() {
        if (this.trialExpired) {
            this.showErrorMessage('انتهت فترة التجربة. يرجى إدخال رمز التفعيل.');
            return false;
        }

        if (!this.trialStarted) {
            this.startTrial();
        }

        // Redirect to login page
        this.redirectToLogin();
        return true;
    }

    // Check if user can access the system
    canAccessSystem() {
        return this.isActivated || (!this.trialExpired && this.trialStarted);
    }

    // Get system status for display
    getSystemStatus() {
        if (this.isActivated) {
            return {
                status: 'activated',
                message: 'النظام مفعل',
                class: 'success'
            };
        } else if (this.trialExpired) {
            return {
                status: 'expired',
                message: 'انتهت فترة التجربة',
                class: 'danger'
            };
        } else if (this.trialStarted) {
            return {
                status: 'trial',
                message: `${this.remainingDays} أيام متبقية`,
                class: this.remainingDays <= 1 ? 'warning' : 'info'
            };
        } else {
            return {
                status: 'not_started',
                message: 'لم تبدأ فترة التجربة',
                class: 'info'
            };
        }
    }

    // Utility methods
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }

    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }

    showMessage(message, type) {
        // Prevent duplicate messages
        if (this.lastMessage === message && this.lastMessageTime && (Date.now() - this.lastMessageTime) < 2000) {
            return;
        }

        this.lastMessage = message;
        this.lastMessageTime = Date.now();

        // Remove existing messages
        const existingMessages = document.querySelectorAll('.activation-message');
        existingMessages.forEach(msg => msg.remove());

        // Create new message
        const messageDiv = document.createElement('div');
        messageDiv.className = `status-box ${type} activation-message`;

        const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : '⚠️';
        messageDiv.innerHTML = `
            <div class="status-icon">${icon}</div>
            <div class="status-text">${message}</div>
        `;

        // Insert message
        const statusSection = document.querySelector('.status-section');
        if (statusSection) {
            statusSection.appendChild(messageDiv);
        }

        // Auto remove after 4 seconds
        setTimeout(() => {
            if (messageDiv && messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 4000);
    }

    redirectToLogin() {
        window.location.href = 'login.html';
    }

    // Format date in Latin format (YYYY-MM-DD)
    formatDate(date) {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    // Get activation info for admin panel
    getActivationInfo() {
        const trialData = this.getTrialData();
        return {
            isActivated: this.isActivated,
            trialStarted: this.trialStarted,
            trialExpired: this.trialExpired,
            remainingDays: this.remainingDays,
            sessions: trialData.sessions || 0,
            maxSessions: this.maxTrialSessions,
            timestamps: trialData.timestamps || [],
            isTampered: this.isTrialTampered(trialData),
            activationDate: this.getSecureData('activationDate')
        };
    }

    // Development mode functions (for testing)
    resetTrialForTesting() {
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            this.resetTrialData();
            localStorage.removeItem('systemActivated');
            localStorage.removeItem('systemActivated_secure');
            localStorage.removeItem('activationDate');
            localStorage.removeItem('activationDate_secure');
            console.log('Trial data reset for testing');
            window.location.reload();
        } else {
            console.warn('Reset function only available in development mode');
        }
    }

    // Add debug information to console
    debugInfo() {
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            const info = this.getActivationInfo();
            console.group('🔐 Activation System Debug Info');
            console.log('📊 Status:', info.isActivated ? '✅ Activated' : '⏳ Trial Mode');
            console.log('📅 Remaining Days:', info.remainingDays);
            console.log('🔢 Sessions:', `${info.sessions}/${info.maxSessions}`);
            console.log('🛡️ Data Integrity:', info.isTampered ? '❌ Compromised' : '✅ Valid');
            console.log('⏰ Timestamps:', info.timestamps.length);
            if (info.timestamps.length > 0) {
                const firstSession = new Date(Math.min(...info.timestamps));
                const lastSession = new Date(Math.max(...info.timestamps));
                console.log('🕐 First Session:', firstSession.toLocaleString('ar-SA'));
                console.log('🕐 Last Session:', lastSession.toLocaleString('ar-SA'));
            }
            console.groupEnd();
        }
    }
}

// Global activation system instance
let activationSystem;

// Initialize activation system
function initializeActivationSystem() {
    activationSystem = new ActivationSystem();

    // Show debug info in development mode
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        setTimeout(() => {
            activationSystem.debugInfo();
            console.log('💡 Development Mode Commands:');
            console.log('- activationSystem.resetTrialForTesting() : Reset trial data');
            console.log('- activationSystem.debugInfo() : Show debug information');
            console.log('- activationSystem.getActivationInfo() : Get detailed status');
        }, 1000);
    }
}

// Global functions for HTML onclick events
function activateSystem() {
    const codeInput = document.getElementById('activationCode');
    const code = codeInput.value.trim();

    if (!code) {
        activationSystem.showErrorMessage('يرجى إدخال رمز التفعيل');
        return;
    }

    activationSystem.activateSystem(code);
}

function enterTrialMode() {
    activationSystem.enterTrialMode();
}

// Check access on protected pages
function checkSystemAccess() {
    if (!activationSystem) {
        activationSystem = new ActivationSystem();
    }

    if (!activationSystem.canAccessSystem()) {
        window.location.href = 'index.html';
        return false;
    }

    return true;
}
