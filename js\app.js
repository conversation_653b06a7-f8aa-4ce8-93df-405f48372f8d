// Main Application JavaScript
class CorrespondenceApp {
    constructor() {
        this.currentUser = null;
        this.currentPage = 1;
        this.pageSize = 10;
        this.init();
    }

    init() {
        this.currentUser = db.getCurrentUser();
        this.setupEventListeners();
        this.loadPageContent();
    }

    setupEventListeners() {
        // Global event listeners
        document.addEventListener('DOMContentLoaded', () => {
            this.initializePage();
        });

        // Handle form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('correspondence-form')) {
                e.preventDefault();
                this.handleFormSubmission(e.target);
            }
        });

        // Handle search inputs
        document.addEventListener('input', (e) => {
            if (e.target.classList.contains('search-input')) {
                this.debounce(() => this.handleSearch(e.target), 300)();
            }
        });
    }

    initializePage() {
        const pageName = this.getCurrentPageName();

        switch (pageName) {
            case 'login':
                this.initializeLoginPage();
                break;
            case 'dashboard':
                this.initializeDashboard();
                break;
            case 'incoming':
                this.initializeIncomingPage();
                break;
            case 'outgoing':
                this.initializeOutgoingPage();
                break;
            case 'entities':
                this.initializeEntitiesPage();
                break;
            case 'users':
                this.initializeUsersPage();
                break;
        }
    }

    getCurrentPageName() {
        const path = window.location.pathname;
        const fileName = path.split('/').pop().split('.')[0];
        return fileName === 'index' ? 'activation' : fileName;
    }

    // Login functionality
    initializeLoginPage() {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }
    }

    handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        if (!username || !password) {
            this.showMessage('يرجى إدخال اسم المستخدم وكلمة المرور', 'danger');
            return;
        }

        const user = db.authenticateUser(username, password);

        if (user) {
            this.currentUser = user;
            this.showMessage('تم تسجيل الدخول بنجاح', 'success');
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 1500);
        } else {
            this.showMessage('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger');
        }
    }

    // Dashboard functionality
    initializeDashboard() {
        this.loadDashboardStats();
        this.loadRecentCorrespondence();
    }

    loadDashboardStats() {
        const stats = db.getStatistics();

        // Update stat cards
        this.updateStatCard('totalIncoming', stats.totalIncoming);
        this.updateStatCard('totalOutgoing', stats.totalOutgoing);
        this.updateStatCard('pendingIncoming', stats.pendingIncoming);
        this.updateStatCard('totalEntities', stats.totalEntities);
    }

    updateStatCard(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
    }

    loadRecentCorrespondence() {
        const recentIncoming = db.getPaginatedRecords('incoming', 1, 5);
        const recentOutgoing = db.getPaginatedRecords('outgoing', 1, 5);

        this.renderRecentTable('recentIncomingTable', recentIncoming.records, 'incoming');
        this.renderRecentTable('recentOutgoingTable', recentOutgoing.records, 'outgoing');
    }

    renderRecentTable(tableId, records, type) {
        const tableBody = document.querySelector(`#${tableId} tbody`);
        if (!tableBody) return;

        tableBody.innerHTML = '';

        if (records.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="4" class="text-center">لا توجد مراسلات</td></tr>';
            return;
        }

        records.forEach(record => {
            const row = document.createElement('tr');

            if (type === 'incoming') {
                row.innerHTML = `
                    <td>${record.registrationNumber || ''}</td>
                    <td>${record.sendingEntity || ''}</td>
                    <td>${this.formatDate(record.receptionDate)}</td>
                    <td>${record.subject || ''}</td>
                `;
            } else {
                row.innerHTML = `
                    <td>${record.registrationNumber || ''}</td>
                    <td>${record.receivingEntity || ''}</td>
                    <td>${this.formatDate(record.sendingDate)}</td>
                    <td>${record.subject || ''}</td>
                `;
            }

            tableBody.appendChild(row);
        });
    }

    // Correspondence management
    initializeIncomingPage() {
        this.loadCorrespondenceTable('incoming');
        this.setupCorrespondenceFilters('incoming');
        this.loadEntitiesForSelect();
    }

    initializeOutgoingPage() {
        this.loadCorrespondenceTable('outgoing');
        this.setupCorrespondenceFilters('outgoing');
        this.loadEntitiesForSelect();
    }

    loadEntitiesForSelect() {
        const entities = db.getTable('entities');
        const selects = document.querySelectorAll('.entity-select');

        selects.forEach(select => {
            select.innerHTML = '<option value="">اختر الجهة</option>';
            entities.forEach(entity => {
                const option = document.createElement('option');
                option.value = entity.name;
                option.textContent = entity.name;
                select.appendChild(option);
            });
        });
    }

    loadCorrespondenceTable(type) {
        const searchTerm = document.getElementById('searchInput')?.value || '';
        const result = db.getPaginatedRecords(type, this.currentPage, this.pageSize, searchTerm);

        this.renderCorrespondenceTable(type, result.records);
        this.renderPagination(result.pagination);
    }

    renderCorrespondenceTable(type, records) {
        const tableBody = document.querySelector('#correspondenceTable tbody');
        if (!tableBody) return;

        tableBody.innerHTML = '';

        if (records.length === 0) {
            const colspan = type === 'incoming' ? 9 : 9;
            tableBody.innerHTML = `<tr><td colspan="${colspan}" class="text-center">لا توجد مراسلات</td></tr>`;
            return;
        }

        records.forEach(record => {
            const row = document.createElement('tr');

            if (type === 'incoming') {
                row.innerHTML = `
                    <td>${record.registrationNumber || ''}</td>
                    <td>${record.sendingEntity || ''}</td>
                    <td>${this.formatDate(record.receptionDate)}</td>
                    <td>${record.subject || ''}</td>
                    <td>${record.responsiblePerson || ''}</td>
                    <td>${record.responseDate ? this.formatDate(record.responseDate) : '<span class="badge bg-warning">معلق</span>'}</td>
                    <td>${record.hasAttachments ? '<i class="fas fa-paperclip text-success"></i>' : ''}</td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="app.editRecord('${type}', ${record.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="app.deleteRecord('${type}', ${record.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
            } else {
                row.innerHTML = `
                    <td>${record.registrationNumber || ''}</td>
                    <td>${record.receivingEntity || ''}</td>
                    <td>${this.formatDate(record.sendingDate)}</td>
                    <td>${record.subject || ''}</td>
                    <td>${record.internalReference || ''}</td>
                    <td>${record.responsiblePerson || ''}</td>
                    <td>${record.hasAttachments ? '<i class="fas fa-paperclip text-success"></i>' : ''}</td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="app.editRecord('${type}', ${record.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="app.deleteRecord('${type}', ${record.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
            }

            tableBody.appendChild(row);
        });
    }

    setupCorrespondenceFilters(type) {
        // Setup search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', () => {
                this.currentPage = 1;
                this.loadCorrespondenceTable(type);
            });
        }
    }

    // Entity management
    initializeEntitiesPage() {
        this.loadEntitiesTable();
    }

    loadEntitiesTable() {
        const entities = db.getTable('entities');
        this.renderEntitiesTable(entities);
    }

    renderEntitiesTable(entities) {
        const tableBody = document.querySelector('#entitiesTable tbody');
        if (!tableBody) return;

        tableBody.innerHTML = '';

        entities.forEach(entity => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${entity.name}</td>
                <td>${entity.type}</td>
                <td>${entity.phone || ''}</td>
                <td>${entity.email || ''}</td>
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="app.editEntity(${entity.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="app.deleteEntity(${entity.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });
    }

    // User management
    initializeUsersPage() {
        if (!db.hasPermission('manage_users')) {
            this.showMessage('ليس لديك صلاحية لإدارة المستخدمين', 'danger');
            return;
        }
        this.loadUsersTable();
    }

    loadUsersTable() {
        const users = db.getTable('users');
        this.renderUsersTable(users);
    }

    renderUsersTable(users) {
        const tableBody = document.querySelector('#usersTable tbody');
        if (!tableBody) return;

        tableBody.innerHTML = '';

        users.forEach(user => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${user.fullName}</td>
                <td>${user.username}</td>
                <td>${this.getRoleDisplayName(user.role)}</td>
                <td>${user.email || ''}</td>
                <td>
                    <span class="badge bg-${user.isActive ? 'success' : 'danger'}">
                        ${user.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="app.editUser(${user.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${user.id !== 1 ? `<button class="btn btn-sm btn-danger" onclick="app.deleteUser(${user.id})"><i class="fas fa-trash"></i></button>` : ''}
                </td>
            `;
            tableBody.appendChild(row);
        });
    }

    // Utility methods
    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-CA'); // YYYY-MM-DD format
    }

    getRoleDisplayName(role) {
        const roles = {
            'administrator': 'مدير',
            'employee': 'موظف',
            'readonly': 'قراءة فقط'
        };
        return roles[role] || role;
    }

    showMessage(message, type = 'info') {
        // Create and show toast message
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    loadPageContent() {
        // Load page-specific content
        const pageName = this.getCurrentPageName();

        if (pageName !== 'activation' && pageName !== 'login') {
            // Check if user is logged in
            if (!this.currentUser) {
                window.location.href = 'login.html';
                return;
            }

            // Update user info in navigation
            this.updateUserInfo();
        }
    }

    updateUserInfo() {
        const userNameElement = document.getElementById('currentUserName');
        const userRoleElement = document.getElementById('currentUserRole');

        if (userNameElement && this.currentUser) {
            userNameElement.textContent = this.currentUser.fullName;
        }

        if (userRoleElement && this.currentUser) {
            userRoleElement.textContent = this.getRoleDisplayName(this.currentUser.role);
        }
    }

    logout() {
        db.logoutUser();
        window.location.href = 'login.html';
    }

    // CRUD operations for correspondence
    addCorrespondence(type, formData) {
        const record = {
            registrationNumber: formData.registrationNumber,
            subject: formData.subject,
            notes: formData.notes,
            responsiblePerson: formData.responsiblePerson,
            hasAttachments: formData.hasAttachments === 'true'
        };

        if (type === 'incoming') {
            record.sendingEntity = formData.sendingEntity;
            record.receptionDate = formData.receptionDate;
            record.responseDate = formData.responseDate || null;
        } else {
            record.receivingEntity = formData.receivingEntity;
            record.sendingDate = formData.sendingDate;
            record.internalReference = formData.internalReference;
        }

        const result = db.insertRecord(type, record);
        this.showMessage('تم إضافة المراسلة بنجاح', 'success');
        this.loadCorrespondenceTable(type);
        return result;
    }

    editRecord(type, id) {
        const record = db.getRecord(type, id);
        if (record) {
            this.populateForm(type, record);
            const modal = new bootstrap.Modal(document.getElementById('correspondenceModal'));
            modal.show();
        }
    }

    deleteRecord(type, id) {
        if (confirm('هل أنت متأكد من حذف هذه المراسلة؟')) {
            if (db.deleteRecord(type, id)) {
                this.showMessage('تم حذف المراسلة بنجاح', 'success');
                this.loadCorrespondenceTable(type);
            } else {
                this.showMessage('حدث خطأ أثناء الحذف', 'danger');
            }
        }
    }

    populateForm(type, record) {
        // Populate form fields with record data
        Object.keys(record).forEach(key => {
            const field = document.getElementById(key);
            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = record[key];
                } else {
                    field.value = record[key] || '';
                }
            }
        });

        // Store record ID for updates
        const form = document.getElementById('correspondenceForm');
        if (form) {
            form.dataset.recordId = record.id;
        }
    }

    renderPagination(pagination) {
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer) return;

        let html = '<nav><ul class="pagination justify-content-center">';

        // Previous button
        html += `<li class="page-item ${!pagination.hasPrev ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="app.changePage(${pagination.currentPage - 1})">السابق</a>
        </li>`;

        // Page numbers
        for (let i = 1; i <= pagination.totalPages; i++) {
            html += `<li class="page-item ${i === pagination.currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="app.changePage(${i})">${i}</a>
            </li>`;
        }

        // Next button
        html += `<li class="page-item ${!pagination.hasNext ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="app.changePage(${pagination.currentPage + 1})">التالي</a>
        </li>`;

        html += '</ul></nav>';
        paginationContainer.innerHTML = html;
    }

    changePage(page) {
        this.currentPage = page;
        const pageName = this.getCurrentPageName();
        if (pageName === 'incoming' || pageName === 'outgoing') {
            this.loadCorrespondenceTable(pageName);
        }
    }
}

// Global app instance
const app = new CorrespondenceApp();
