<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الجهات الخارجية - نظام إدارة المراسلات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-envelope-open-text me-2"></i>
                نظام إدارة المراسلات
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="incoming.html">
                            <i class="fas fa-inbox me-1"></i>
                            المراسلات الواردة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="outgoing.html">
                            <i class="fas fa-paper-plane me-1"></i>
                            المراسلات الصادرة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="entities.html">
                            <i class="fas fa-building me-1"></i>
                            الجهات الخارجية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.html">
                            <i class="fas fa-users me-1"></i>
                            إدارة المستخدمين
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <span id="currentUserName">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><span class="dropdown-item-text">الدور: <span id="currentUserRole">موظف</span></span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="app.logout()">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2 class="card-title text-gradient mb-0">
                                    <i class="fas fa-building me-2"></i>
                                    إدارة الجهات الخارجية
                                </h2>
                                <p class="card-text text-muted">
                                    إضافة وتعديل بيانات الجهات والمؤسسات التي يتم التراسل معها
                                </p>
                            </div>
                            <button class="btn btn-info btn-lg" data-bs-toggle="modal" data-bs-target="#entityModal">
                                <i class="fas fa-plus me-2"></i>
                                إضافة جهة خارجية
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="searchInput" 
                                           placeholder="البحث في الجهات...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="typeFilter">
                                    <option value="">جميع الأنواع</option>
                                    <option value="وزارة">وزارة</option>
                                    <option value="شركة">شركة</option>
                                    <option value="مؤسسة">مؤسسة</option>
                                    <option value="فرد">فرد</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-secondary w-100" onclick="exportEntities()">
                                    <i class="fas fa-download me-2"></i>
                                    تصدير البيانات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Entities Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة الجهات الخارجية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="entitiesTable">
                                <thead>
                                    <tr>
                                        <th>اسم الجهة</th>
                                        <th>النوع</th>
                                        <th>الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>العنوان</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="6" class="text-center">جاري التحميل...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div class="modal fade" id="entityModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        إضافة جهة خارجية
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="entityForm">
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="name" class="form-label">اسم الجهة *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="type" class="form-label">نوع الجهة *</label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">اختر النوع</option>
                                    <option value="وزارة">وزارة</option>
                                    <option value="شركة">شركة</option>
                                    <option value="مؤسسة">مؤسسة</option>
                                    <option value="فرد">فرد</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="phone" class="form-label">الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="fax" class="form-label">الفاكس</label>
                                <input type="tel" class="form-control" id="fax" name="fax">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-info" onclick="saveEntity()">
                        <i class="fas fa-save me-2"></i>
                        حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/database.js"></script>
    <script src="js/activation.js"></script>
    <script src="js/app.js"></script>
    
    <script>
        // Page specific functions
        function saveEntity() {
            const form = document.getElementById('entityForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            // Validate required fields
            if (!data.name || !data.type) {
                app.showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }
            
            const recordId = form.dataset.recordId;
            
            if (recordId) {
                // Update existing record
                db.updateRecord('entities', recordId, data);
                app.showMessage('تم تحديث الجهة بنجاح', 'success');
            } else {
                // Add new record
                db.insertRecord('entities', data);
                app.showMessage('تم إضافة الجهة بنجاح', 'success');
            }
            
            // Close modal and refresh table
            const modal = bootstrap.Modal.getInstance(document.getElementById('entityModal'));
            modal.hide();
            form.reset();
            delete form.dataset.recordId;
            loadEntitiesTable();
        }
        
        function editEntity(id) {
            const entity = db.getRecord('entities', id);
            if (entity) {
                // Populate form
                Object.keys(entity).forEach(key => {
                    const field = document.getElementById(key);
                    if (field) {
                        field.value = entity[key] || '';
                    }
                });
                
                // Store record ID
                const form = document.getElementById('entityForm');
                form.dataset.recordId = entity.id;
                
                // Update modal title
                document.querySelector('#entityModal .modal-title').innerHTML = 
                    '<i class="fas fa-edit me-2"></i>تعديل جهة خارجية';
                
                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('entityModal'));
                modal.show();
            }
        }
        
        function deleteEntity(id) {
            if (confirm('هل أنت متأكد من حذف هذه الجهة؟')) {
                if (db.deleteRecord('entities', id)) {
                    app.showMessage('تم حذف الجهة بنجاح', 'success');
                    loadEntitiesTable();
                } else {
                    app.showMessage('حدث خطأ أثناء الحذف', 'danger');
                }
            }
        }
        
        function loadEntitiesTable() {
            const searchTerm = document.getElementById('searchInput').value;
            const typeFilter = document.getElementById('typeFilter').value;
            
            let entities = db.getTable('entities');
            
            // Apply search filter
            if (searchTerm) {
                entities = entities.filter(entity => 
                    entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    entity.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    (entity.email && entity.email.toLowerCase().includes(searchTerm.toLowerCase()))
                );
            }
            
            // Apply type filter
            if (typeFilter) {
                entities = entities.filter(entity => entity.type === typeFilter);
            }
            
            renderEntitiesTable(entities);
        }
        
        function renderEntitiesTable(entities) {
            const tableBody = document.querySelector('#entitiesTable tbody');
            tableBody.innerHTML = '';
            
            if (entities.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد جهات</td></tr>';
                return;
            }
            
            entities.forEach(entity => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${entity.name}</td>
                    <td><span class="badge bg-secondary">${entity.type}</span></td>
                    <td>${entity.phone || ''}</td>
                    <td>${entity.email || ''}</td>
                    <td>${entity.address || ''}</td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editEntity(${entity.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteEntity(${entity.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }
        
        function exportEntities() {
            const data = db.exportTable('entities');
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `entities_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            if (!checkSystemAccess()) {
                return;
            }
            
            loadEntitiesTable();
            
            // Search functionality
            document.getElementById('searchInput').addEventListener('input', loadEntitiesTable);
            document.getElementById('typeFilter').addEventListener('change', loadEntitiesTable);
            
            // Reset modal on close
            document.getElementById('entityModal').addEventListener('hidden.bs.modal', function() {
                document.getElementById('entityForm').reset();
                delete document.getElementById('entityForm').dataset.recordId;
                document.querySelector('#entityModal .modal-title').innerHTML = 
                    '<i class="fas fa-plus me-2"></i>إضافة جهة خارجية';
            });
        });
    </script>
</body>
</html>
