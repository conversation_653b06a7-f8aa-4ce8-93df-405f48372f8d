/* Arabic RTL Styling for Administrative Correspondence Management System */

/* Import Arabic Fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

/* Root Variables */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Amiri', serif;
    font-weight: 700;
}

.arabic-text {
    font-family: 'Cairo', sans-serif;
    line-height: 1.8;
}

/* Cards and Containers */
.card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border-bottom: none;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 12px 24px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(45deg, var(--secondary-color), #5dade2);
    border: none;
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #58d68d);
    border: none;
}

.btn-danger {
    background: linear-gradient(45deg, var(--danger-color), #ec7063);
    border: none;
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-color), #f7dc6f);
    border: none;
    color: #fff;
}

/* Form Controls */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 12px 16px;
    font-size: 16px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(45deg, var(--primary-color), #34495e);
    color: white;
    border: none;
    font-weight: 600;
    padding: 16px;
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

.table tbody td {
    padding: 16px;
    vertical-align: middle;
    border-color: #e9ecef;
}

/* Navigation */
.navbar {
    box-shadow: var(--box-shadow);
    background: linear-gradient(45deg, var(--primary-color), #34495e) !important;
}

.navbar-brand {
    font-family: 'Amiri', serif;
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--warning-color) !important;
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: none;
    font-weight: 500;
}

.alert-info {
    background: linear-gradient(45deg, #d6eaf8, #aed6f1);
    color: #1b4f72;
}

.alert-success {
    background: linear-gradient(45deg, #d5f4e6, #a9dfbf);
    color: #0e4b2a;
}

.alert-danger {
    background: linear-gradient(45deg, #fadbd8, #f1948a);
    color: #641e16;
}

.alert-warning {
    background: linear-gradient(45deg, #fcf3cf, #f7dc6f);
    color: #7d6608;
}

/* Sidebar */
.sidebar {
    background: linear-gradient(180deg, var(--primary-color), #34495e);
    min-height: 100vh;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
    color: #bdc3c7;
    padding: 15px 20px;
    border-radius: var(--border-radius);
    margin: 5px 10px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

/* Modal */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(45deg, var(--primary-color), #34495e);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* Badges */
.badge {
    border-radius: 20px;
    padding: 8px 12px;
    font-weight: 500;
}

/* Custom Utilities */
.text-gradient {
    background: linear-gradient(45deg, var(--secondary-color), #5dade2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
    }
    
    .card-body {
        padding: 20px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .table-responsive {
        font-size: 14px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* Loading Spinner */
.spinner-custom {
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--secondary-color);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
