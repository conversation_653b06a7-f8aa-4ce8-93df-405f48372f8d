/* SIMPLE STABLE CSS - NO MOVEMENT */

/* Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    transform: none !important;
    transition: none !important;
    animation: none !important;
}

/* Fixed Page */
html, body {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    font-family: Arial, sans-serif;
    direction: rtl;
    background: #1a365d;
}

/* Main Container */
.main-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* Activation Box */
.activation-box {
    background: white;
    border-radius: 10px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Header */
.header-section {
    background: #1a365d;
    color: white;
    padding: 30px 20px;
    text-align: center;
    border-radius: 10px 10px 0 0;
}

.logo-icon {
    font-size: 40px;
    margin-bottom: 10px;
}

.system-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 5px;
}

.system-subtitle {
    font-size: 14px;
    opacity: 0.9;
}

/* Status */
.status-section {
    padding: 20px;
}

.status-box {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    min-height: 50px;
}

.status-box.info { background: #3b82f6; color: white; }
.status-box.success { background: #22c55e; color: white; }
.status-box.warning { background: #f59e0b; color: white; }
.status-box.error { background: #ef4444; color: white; }

.status-icon {
    font-size: 20px;
    margin-left: 10px;
}

.status-text {
    font-size: 14px;
    font-weight: bold;
}

/* Form */
.form-section {
    padding: 0 20px 20px;
}

.input-group {
    margin-bottom: 15px;
}

.input-label {
    display: block;
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.text-input {
    width: 100%;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    text-align: center;
    background: white;
    color: #333;
    outline: none;
}

.text-input:focus {
    border-color: #1a365d;
}

.input-help {
    font-size: 12px;
    color: #666;
    text-align: center;
    margin-top: 5px;
}

/* Buttons */
.action-btn {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 50px;
}

.action-btn.primary { background: #1a365d; color: white; }
.action-btn.success { background: #22c55e; color: white; }
.action-btn.warning { background: #f59e0b; color: white; }
.action-btn.error { background: #ef4444; color: white; }

/* Footer */
.footer-section {
    padding: 20px;
    background: #f5f5f5;
    border-radius: 0 0 10px 10px;
    text-align: center;
}

.system-info {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.security-info {
    font-size: 11px;
    color: #22c55e;
    margin-bottom: 5px;
}

.dev-info {
    font-size: 10px;
    color: #f59e0b;
    background: #fff3cd;
    padding: 5px 10px;
    border-radius: 5px;
    margin-top: 5px;
    display: inline-block;
}

/* END OF CSS */
