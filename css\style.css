/* Arabic RTL Styling for Administrative Correspondence Management System */

/* Ultra Stable Template - Arabic RTL */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

/* Root Variables */
:root {
    --primary: #1a365d;
    --primary-light: #2d5a87;
    --success: #22c55e;
    --success-light: #4ade80;
    --error: #ef4444;
    --error-light: #f87171;
    --warning: #f59e0b;
    --warning-light: #fbbf24;
    --info: #3b82f6;
    --info-light: #60a5fa;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --white: #ffffff;
    --radius: 12px;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Ultra Stable Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

*,
*::before,
*::after {
    transform: none !important;
    transition: none !important;
    animation: none !important;
    will-change: auto !important;
}

html {
    height: 100%;
    overflow: hidden;
    font-size: 16px;
}

body {
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, sans-serif;
    direction: rtl;
    text-align: right;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    color: var(--gray-800);
    line-height: 1.6;
}

/* Main Container */
.main-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    overflow: hidden;
}

/* Activation Box */
.activation-box {
    background: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 420px;
    padding: 0;
    overflow: hidden;
    position: relative;
}

/* Header Section */
.header-section {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: var(--white);
    padding: 32px 24px;
    text-align: center;
    border-radius: var(--radius) var(--radius) 0 0;
}

.logo-icon {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

.system-title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1.3;
}

.system-subtitle {
    font-size: 14px;
    opacity: 0.9;
    margin: 0;
}

/* Status Section */
.status-section {
    padding: 24px;
}

.status-box {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-radius: var(--radius);
    margin-bottom: 16px;
    min-height: 60px;
    position: relative;
}

.status-box.info {
    background: var(--info);
    color: var(--white);
}

.status-box.success {
    background: var(--success);
    color: var(--white);
}

.status-box.warning {
    background: var(--warning);
    color: var(--white);
}

.status-box.error {
    background: var(--error);
    color: var(--white);
}

.status-icon {
    font-size: 24px;
    margin-left: 12px;
    flex-shrink: 0;
}

.status-text {
    font-size: 16px;
    font-weight: 600;
    flex: 1;
}

/* Form Section */
.form-section {
    padding: 0 24px 24px;
}

.input-group {
    margin-bottom: 20px;
}

.input-label {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 8px;
}

.text-input {
    width: 100%;
    padding: 16px;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius);
    font-size: 16px;
    text-align: center;
    background: var(--white);
    color: var(--gray-800);
    outline: none;
    font-family: inherit;
}

.text-input:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(26, 54, 93, 0.1);
}

.input-help {
    font-size: 14px;
    color: var(--gray-500);
    text-align: center;
    margin-top: 8px;
}

/* Action Buttons */
.action-btn {
    width: 100%;
    padding: 16px 24px;
    border: none;
    border-radius: var(--radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 56px;
    font-family: inherit;
    outline: none;
    position: relative;
}

.action-btn.primary {
    background: var(--primary);
    color: var(--white);
}

.action-btn.primary:hover {
    background: var(--primary-light);
}

.action-btn.success {
    background: var(--success);
    color: var(--white);
}

.action-btn.success:hover {
    background: var(--success-light);
}

.action-btn.warning {
    background: var(--warning);
    color: var(--white);
}

.action-btn.warning:hover {
    background: var(--warning-light);
}

.action-btn.error {
    background: var(--error);
    color: var(--white);
}

.action-btn.error:hover {
    background: var(--error-light);
}

/* Footer Section */
.footer-section {
    padding: 20px 24px;
    background: var(--gray-50);
    border-radius: 0 0 var(--radius) var(--radius);
    text-align: center;
}

.system-info {
    font-size: 14px;
    color: var(--gray-600);
    margin-bottom: 8px;
}

.security-info {
    font-size: 13px;
    color: var(--success);
    margin-bottom: 8px;
}

.dev-info {
    font-size: 12px;
    color: var(--warning);
    background: var(--warning-light);
    padding: 8px 12px;
    border-radius: 6px;
    margin-top: 8px;
    display: inline-block;
}

/* Responsive Design */
@media (max-width: 480px) {
    .main-container {
        padding: 16px;
    }

    .activation-box {
        max-width: 100%;
    }

    .header-section {
        padding: 24px 20px;
    }

    .logo-icon {
        font-size: 40px;
        margin-bottom: 12px;
    }

    .system-title {
        font-size: 20px;
    }

    .system-subtitle {
        font-size: 13px;
    }

    .status-section,
    .form-section {
        padding: 20px;
    }

    .footer-section {
        padding: 16px 20px;
    }

    .text-input,
    .action-btn {
        padding: 14px 16px;
        font-size: 15px;
    }

    .status-box {
        padding: 14px 16px;
        min-height: 56px;
    }

    .status-icon {
        font-size: 20px;
        margin-left: 10px;
    }

    .status-text {
        font-size: 15px;
    }
}

/* Accessibility */
.action-btn:focus,
.text-input:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    body {
        background: white !important;
    }

    .main-container {
        position: static;
        padding: 0;
    }

    .activation-box {
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
}

/* End of Ultra Stable Template */
